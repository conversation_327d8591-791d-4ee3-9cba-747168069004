-- ======================================================
-- 刊登成功列表日期查询测试脚本
-- 创建时间: 2025-01-27
-- 说明: 用于验证同一天日期查询和跨天查询的修复效果
-- ======================================================

-- 测试数据插入（用于测试）
-- 注意：仅用于测试环境，生产环境请勿执行
INSERT INTO publish_success_list (
    platform, source, sku, spu, account, site, creator, 
    create_time, created_at, updated_at
) VALUES 
-- 2025-07-08 的测试数据
('Amazon', 'RPA', 'TEST-SKU-001', 'TEST-SPU-001', 'test_account', 'US', 'system', 
 '2025-07-08 09:30:15', '2025-07-08 09:30:15', '2025-07-08 09:30:15'),
('Amazon', 'RPA', 'TEST-SKU-002', 'TEST-SPU-002', 'test_account', 'US', 'system', 
 '2025-07-08 14:20:30', '2025-07-08 14:20:30', '2025-07-08 14:20:30'),
('Amazon', 'RPA', 'TEST-SKU-003', 'TEST-SPU-003', 'test_account', 'US', 'system', 
 '2025-07-08 23:45:59', '2025-07-08 23:45:59', '2025-07-08 23:45:59'),
-- 2025-07-09 的测试数据
('Amazon', 'RPA', 'TEST-SKU-004', 'TEST-SPU-004', 'test_account', 'US', 'system', 
 '2025-07-09 10:15:45', '2025-07-09 10:15:45', '2025-07-09 10:15:45'),
('Amazon', 'RPA', 'TEST-SKU-005', 'TEST-SPU-005', 'test_account', 'US', 'system', 
 '2025-07-09 18:30:20', '2025-07-09 18:30:20', '2025-07-09 18:30:20');

-- ======================================================
-- 查询测试
-- ======================================================

-- 测试1：同一天查询 - 修复前会查不到数据
-- 应该返回3条2025-07-08的数据
SELECT 
    sku, 
    create_time, 
    created_at, 
    updated_at
FROM publish_success_list 
WHERE create_time BETWEEN CONCAT('2025-07-08', ' 00:00:00') AND CONCAT('2025-07-08', ' 23:59:59')
ORDER BY create_time;

-- 测试2：跨天查询 - 应该返回所有5条数据
SELECT 
    sku, 
    create_time, 
    created_at, 
    updated_at
FROM publish_success_list 
WHERE create_time BETWEEN CONCAT('2025-07-08', ' 00:00:00') AND CONCAT('2025-07-09', ' 23:59:59')
ORDER BY create_time;

-- 测试3：单天查询 - 应该返回2条2025-07-09的数据
SELECT 
    sku, 
    create_time, 
    created_at, 
    updated_at
FROM publish_success_list 
WHERE create_time BETWEEN CONCAT('2025-07-09', ' 00:00:00') AND CONCAT('2025-07-09', ' 23:59:59')
ORDER BY create_time;

-- 测试4：验证修复前的查询方式（会查不到数据）
SELECT 
    sku, 
    create_time, 
    created_at, 
    updated_at
FROM publish_success_list 
WHERE create_time BETWEEN '2025-07-08' AND '2025-07-08'
ORDER BY create_time;

-- 测试5：验证索引使用情况
EXPLAIN SELECT 
    sku, 
    create_time, 
    created_at, 
    updated_at
FROM publish_success_list 
WHERE create_time BETWEEN CONCAT('2025-07-08', ' 00:00:00') AND CONCAT('2025-07-08', ' 23:59:59')
ORDER BY create_time;

-- 测试6：验证组合索引使用情况
EXPLAIN SELECT 
    sku, 
    create_time, 
    created_at, 
    updated_at
FROM publish_success_list 
WHERE account = 'test_account' 
AND site = 'US' 
AND create_time BETWEEN CONCAT('2025-07-08', ' 00:00:00') AND CONCAT('2025-07-08', ' 23:59:59')
ORDER BY create_time;

-- ======================================================
-- 清理测试数据
-- ======================================================

-- 清理测试数据（测试完成后执行）
DELETE FROM publish_success_list WHERE sku LIKE 'TEST-SKU-%';

-- ======================================================
-- 预期结果说明
-- ======================================================

/*
预期测试结果：

测试1 (同一天查询):
- 应该返回3条记录：TEST-SKU-001, TEST-SKU-002, TEST-SKU-003
- 时间范围：2025-07-08 09:30:15 到 2025-07-08 23:45:59

测试2 (跨天查询):
- 应该返回5条记录：TEST-SKU-001 到 TEST-SKU-005
- 时间范围：2025-07-08 09:30:15 到 2025-07-09 18:30:20

测试3 (单天查询):
- 应该返回2条记录：TEST-SKU-004, TEST-SKU-005
- 时间范围：2025-07-09 10:15:45 到 2025-07-09 18:30:20

测试4 (修复前查询):
- 应该返回0条记录（证明修复的必要性）

测试5 (索引使用):
- 应该显示使用了 idx_publish_success_create_time 索引
- type: range
- key: idx_publish_success_create_time

测试6 (组合索引使用):
- 应该显示使用了 idx_publish_success_account_site_time 索引
- type: range
- key: idx_publish_success_account_site_time
*/

-- ======================================================
-- 修复验证清单
-- ======================================================

/*
验证清单：
□ 同一天查询（2025-07-08 - 2025-07-08）能正确返回当天所有数据
□ 跨天查询（2025-07-08 - 2025-07-09）能正确返回范围内所有数据
□ 前端时间显示格式正确显示到时分秒
□ 查询性能良好，正确使用索引
□ 边界时间测试（00:00:00 和 23:59:59）正确
*/ 