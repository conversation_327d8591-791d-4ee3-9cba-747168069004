package com.ruoyi.data.util;

import com.ruoyi.data.domain.PurchaseOrderSkuDim;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 唯一约束验证工具类
 * 用于验证数据的唯一性约束
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
public class UniqueConstraintValidator {

    /**
     * 验证结果类
     */
    public static class ValidationResult {
        private boolean valid;
        private String message;
        private List<String> duplicateKeys;
        private List<String> invalidRecords;

        public ValidationResult(boolean valid, String message) {
            this.valid = valid;
            this.message = message;
            this.duplicateKeys = new ArrayList<>();
            this.invalidRecords = new ArrayList<>();
        }

        // Getters and Setters
        public boolean isValid() { return valid; }
        public void setValid(boolean valid) { this.valid = valid; }
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
        public List<String> getDuplicateKeys() { return duplicateKeys; }
        public void setDuplicateKeys(List<String> duplicateKeys) { this.duplicateKeys = duplicateKeys; }
        public List<String> getInvalidRecords() { return invalidRecords; }
        public void setInvalidRecords(List<String> invalidRecords) { this.invalidRecords = invalidRecords; }
    }

    /**
     * 验证采购单SKU维度数据的唯一性约束
     * 
     * @param dataList 待验证的数据列表
     * @return 验证结果
     */
    public static ValidationResult validatePurchaseOrderSkuDimUniqueness(List<PurchaseOrderSkuDim> dataList) {
        ValidationResult result = new ValidationResult(true, "验证通过");
        
        if (dataList == null || dataList.isEmpty()) {
            return result;
        }

        // 1. 检查必填字段
        List<String> invalidRecords = new ArrayList<>();
        for (int i = 0; i < dataList.size(); i++) {
            PurchaseOrderSkuDim item = dataList.get(i);
            if (!StringUtils.hasText(item.getPurchaseOrderNo()) || !StringUtils.hasText(item.getSystemSku())) {
                invalidRecords.add("第" + (i + 1) + "行：采购单号和系统SKU不能为空");
            }
        }

        // 2. 检查数据内部重复
        Map<String, List<Integer>> duplicateMap = new HashMap<>();
        for (int i = 0; i < dataList.size(); i++) {
            PurchaseOrderSkuDim item = dataList.get(i);
            if (StringUtils.hasText(item.getPurchaseOrderNo()) && StringUtils.hasText(item.getSystemSku())) {
                String key = item.getPurchaseOrderNo() + "|" + item.getSystemSku();
                duplicateMap.computeIfAbsent(key, k -> new ArrayList<>()).add(i + 1);
            }
        }

        List<String> duplicateKeys = new ArrayList<>();
        for (Map.Entry<String, List<Integer>> entry : duplicateMap.entrySet()) {
            if (entry.getValue().size() > 1) {
                String[] parts = entry.getKey().split("\\|");
                String duplicateInfo = String.format("采购单号[%s] + 系统SKU[%s] 在第%s行重复", 
                    parts[0], parts[1], entry.getValue().toString());
                duplicateKeys.add(duplicateInfo);
            }
        }

        // 3. 构建验证结果
        if (!invalidRecords.isEmpty() || !duplicateKeys.isEmpty()) {
            result.setValid(false);
            result.setInvalidRecords(invalidRecords);
            result.setDuplicateKeys(duplicateKeys);
            
            StringBuilder message = new StringBuilder("数据验证失败：\n");
            if (!invalidRecords.isEmpty()) {
                message.append("必填字段缺失：\n");
                for (String invalid : invalidRecords) {
                    message.append("  - ").append(invalid).append("\n");
                }
            }
            if (!duplicateKeys.isEmpty()) {
                message.append("数据内部重复：\n");
                for (String duplicate : duplicateKeys) {
                    message.append("  - ").append(duplicate).append("\n");
                }
            }
            result.setMessage(message.toString());
        }

        return result;
    }

    /**
     * 生成唯一键
     * 
     * @param purchaseOrderNo 采购单号
     * @param systemSku 系统SKU
     * @return 唯一键
     */
    public static String generateUniqueKey(String purchaseOrderNo, String systemSku) {
        return purchaseOrderNo + "|" + systemSku;
    }

    /**
     * 从采购单SKU维度对象生成唯一键
     * 
     * @param item 采购单SKU维度对象
     * @return 唯一键
     */
    public static String generateUniqueKey(PurchaseOrderSkuDim item) {
        if (item == null || !StringUtils.hasText(item.getPurchaseOrderNo()) || !StringUtils.hasText(item.getSystemSku())) {
            return null;
        }
        return generateUniqueKey(item.getPurchaseOrderNo(), item.getSystemSku());
    }

    /**
     * 检查数据列表中是否存在指定的唯一键
     * 
     * @param dataList 数据列表
     * @param purchaseOrderNo 采购单号
     * @param systemSku 系统SKU
     * @return 是否存在
     */
    public static boolean containsUniqueKey(List<PurchaseOrderSkuDim> dataList, String purchaseOrderNo, String systemSku) {
        if (dataList == null || dataList.isEmpty()) {
            return false;
        }
        
        String targetKey = generateUniqueKey(purchaseOrderNo, systemSku);
        if (targetKey == null) {
            return false;
        }
        
        for (PurchaseOrderSkuDim item : dataList) {
            String itemKey = generateUniqueKey(item);
            if (targetKey.equals(itemKey)) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * 移除数据列表中的重复项（保留第一个）
     * 
     * @param dataList 数据列表
     * @return 去重后的数据列表
     */
    public static List<PurchaseOrderSkuDim> removeDuplicates(List<PurchaseOrderSkuDim> dataList) {
        if (dataList == null || dataList.isEmpty()) {
            return dataList;
        }

        Set<String> seenKeys = new HashSet<>();
        List<PurchaseOrderSkuDim> uniqueList = new ArrayList<>();
        
        for (PurchaseOrderSkuDim item : dataList) {
            String key = generateUniqueKey(item);
            if (key != null && !seenKeys.contains(key)) {
                seenKeys.add(key);
                uniqueList.add(item);
            }
        }
        
        return uniqueList;
    }

    /**
     * 获取数据列表中的重复项信息
     * 
     * @param dataList 数据列表
     * @return 重复项信息映射（键 -> 重复次数）
     */
    public static Map<String, Integer> getDuplicateInfo(List<PurchaseOrderSkuDim> dataList) {
        if (dataList == null || dataList.isEmpty()) {
            return new HashMap<>();
        }

        Map<String, Integer> countMap = new HashMap<>();
        for (PurchaseOrderSkuDim item : dataList) {
            String key = generateUniqueKey(item);
            if (key != null) {
                countMap.put(key, countMap.getOrDefault(key, 0) + 1);
            }
        }

        // 只返回重复的项
        Map<String, Integer> duplicateMap = new HashMap<>();
        for (Map.Entry<String, Integer> entry : countMap.entrySet()) {
            if (entry.getValue() > 1) {
                duplicateMap.put(entry.getKey(), entry.getValue());
            }
        }

        return duplicateMap;
    }
}
