package com.ruoyi.data.util;

import java.util.List;
import java.util.function.Function;

/**
 * 分页查询函数接口
 * 为流式导出提供统一的分页查询标准
 *
 * <AUTHOR>
 * @date 2025-01-21
 */
@FunctionalInterface
public interface PageQueryFunction<T> extends Function<DataExcelStreamExporter.PageQuery, List<T>> {

    /**
     * 执行分页查询
     *
     * @param pageQuery 分页查询参数
     * @return 查询结果列表
     */
    @Override
    List<T> apply(DataExcelStreamExporter.PageQuery pageQuery);

    /**
     * 创建简单的分页查询函数
     *
     * @param queryFunction 查询函数（接收页码、页大小，返回数据列表）
     * @param <T> 实体类型
     * @return 分页查询函数
     */
    static <T> PageQueryFunction<T> of(BiFunction<Integer, Integer, List<T>> queryFunction) {
        return pageQuery -> queryFunction.apply(pageQuery.getPageNum(), pageQuery.getPageSize());
    }

    /**
     * 双参数函数接口
     */
    @FunctionalInterface
    interface BiFunction<T, U, R> {
        R apply(T t, U u);
    }
}