package com.ruoyi.data.mapper;

import java.util.List;
import com.ruoyi.data.domain.AccountTradeDetail;

/**
 * 账户交易明细Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-05
 */
public interface AccountTradeDetailMapper 
{
    /**
     * 查询账户交易明细
     * 
     * @param id 账户交易明细主键
     * @return 账户交易明细
     */
    public AccountTradeDetail selectAccountTradeDetailById(Long id);

    /**
     * 查询账户交易明细列表
     * 
     * @param accountTradeDetail 账户交易明细
     * @return 账户交易明细集合
     */
    public List<AccountTradeDetail> selectAccountTradeDetailList(AccountTradeDetail accountTradeDetail);

    /**
     * 统计账户交易明细数量
     * 
     * @param accountTradeDetail 账户交易明细查询条件
     * @return 统计结果
     */
    public long countAccountTradeDetail(AccountTradeDetail accountTradeDetail);

    /**
     * 新增账户交易明细
     * 
     * @param accountTradeDetail 账户交易明细
     * @return 结果
     */
    public int insertAccountTradeDetail(AccountTradeDetail accountTradeDetail);

    /**
     * 修改账户交易明细
     * 
     * @param accountTradeDetail 账户交易明细
     * @return 结果
     */
    public int updateAccountTradeDetail(AccountTradeDetail accountTradeDetail);

    /**
     * 删除账户交易明细
     * 
     * @param id 账户交易明细主键
     * @return 结果
     */
    public int deleteAccountTradeDetailById(Long id);

    /**
     * 批量删除账户交易明细
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteAccountTradeDetailByIds(Long[] ids);

    /**
     * 根据交易流水号查询账户交易明细
     * 
     * @param tradeSerial 交易流水号
     * @return 账户交易明细
     */
    public AccountTradeDetail selectAccountTradeDetailByTradeSerial(String tradeSerial);

    /**
     * 批量新增账户交易明细
     * 
     * @param accountTradeDetailList 账户交易明细列表
     * @return 结果
     */
    public int batchInsertAccountTradeDetail(List<AccountTradeDetail> accountTradeDetailList);

    /**
     * 批量更新账户交易明细
     * 
     * @param accountTradeDetailList 账户交易明细列表
     * @return 结果
     */
    public int batchUpdateAccountTradeDetail(List<AccountTradeDetail> accountTradeDetailList);

    /**
     * 根据交易流水号批量查询账户交易明细
     * 
     * @param tradeSerials 交易流水号列表
     * @return 账户交易明细列表
     */
    public List<AccountTradeDetail> selectAccountTradeDetailByTradeSerials(List<String> tradeSerials);

    /**
     * 分页查询账户交易明细列表（用于流式导出全量数据）
     * 
     * @param accountTradeDetail 查询条件
     * @return 账户交易明细集合
     */
    public List<AccountTradeDetail> selectAccountTradeDetailListByPage(AccountTradeDetail accountTradeDetail);

    /**
     * 查询待处理的账户交易明细（flow_status为0或1）
     *
     * @return 待处理的账户交易明细列表
     */
    public List<AccountTradeDetail> selectPendingAccountTradeDetails();

    /**
     * 根据交易单号查询有店铺信息的记录（用于店铺名推导）
     *
     * @param orderCode 交易单号
     * @param tradeTypes 交易类型列表（采购单、订单）
     * @return 有店铺信息的账户交易明细列表
     */
    public List<AccountTradeDetail> selectRecordsWithShopByOrderCode(String orderCode, List<String> tradeTypes);

    /**
     * 根据交易单号查询账户交易明细列表
     *
     * @param orderCode 交易单号
     * @return 账户交易明细列表
     */
    public List<AccountTradeDetail> selectByOrderCode(String orderCode);
}
