package com.ruoyi.data.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.data.domain.PurchaseOrderSkuDim;
import com.ruoyi.data.service.IPurchaseOrderSkuDimService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.data.util.StreamExportContext;
import com.ruoyi.data.util.DataExcelStreamExporter;
import com.ruoyi.data.util.PageQueryFunction;
import com.ruoyi.data.util.DataExcelStreamExporterFactory;
import org.springframework.web.multipart.MultipartFile;

/**
 * 采购单-sku维度Controller
 * 
 * <AUTHOR>
 * @date 2025-07-26
 */
@RestController
@RequestMapping("/data/purchaseOrderSkuDim")
public class PurchaseOrderSkuDimController extends BaseController
{
    @Autowired
    private IPurchaseOrderSkuDimService purchaseOrderSkuDimService;

    @Autowired
    private DataExcelStreamExporterFactory exporterFactory;

    /**
     * 查询采购单-sku维度列表
     */
    @PreAuthorize("@ss.hasPermi('data:purchaseOrderSkuDim:list')")
    @GetMapping("/list")
    public TableDataInfo list(PurchaseOrderSkuDim purchaseOrderSkuDim)
    {
        startPage();
        List<PurchaseOrderSkuDim> list = purchaseOrderSkuDimService.selectPurchaseOrderSkuDimList(purchaseOrderSkuDim);
        return getDataTable(list);
    }

    /**
     * 导出采购单-sku维度列表
     */
    @PreAuthorize("@ss.hasPermi('data:purchaseOrderSkuDim:export')")
    @Log(title = "采购单-sku维度", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PurchaseOrderSkuDim purchaseOrderSkuDim)
    {
        List<PurchaseOrderSkuDim> list = purchaseOrderSkuDimService.selectPurchaseOrderSkuDimList(purchaseOrderSkuDim);
        ExcelUtil<PurchaseOrderSkuDim> util = new ExcelUtil<PurchaseOrderSkuDim>(PurchaseOrderSkuDim.class);
        util.exportExcel(response, list, "采购单-sku维度数据");
    }

    /**
     * 流式导出采购单-sku维度列表（支持大数据量导出）
     */
    @PreAuthorize("@ss.hasPermi('data:purchaseOrderSkuDim:export')")
    @Log(title = "采购单-sku维度流式导出", businessType = BusinessType.EXPORT)
    @PostMapping("/streamExport")
    public void streamExport(HttpServletResponse response, @RequestBody PurchaseOrderSkuDim purchaseOrderSkuDim)
    {
        try {
            // 创建分页查询函数
            PageQueryFunction<PurchaseOrderSkuDim> pageQueryFunction = purchaseOrderSkuDimService.createPageQueryFunction(purchaseOrderSkuDim);

            // 执行流式导出
            DataExcelStreamExporter<PurchaseOrderSkuDim> exporter = exporterFactory.create(PurchaseOrderSkuDim.class);
            exporter.exportToResponse(response, pageQueryFunction, "采购单-sku维度数据");

            // 获取导出上下文
            StreamExportContext context = exporter.getContext();

            // 获取导出进度快照
            StreamExportContext.ExportProgress progress = context.createProgressSnapshot();

            logger.info("流式导出完成，共处理 {} 条记录，耗时 {} 秒", context.getProcessedRecords(), (progress.getElapsedTime() / 1000.0));
        } catch (Exception e) {
            logger.error("采购单-sku维度流式导出失败", e);
            try {
                // 重置响应状态，返回错误信息
                response.reset();
                response.setContentType("application/json;charset=utf-8");
                response.getWriter().write("{\"code\":500,\"msg\":\"导出失败：" + e.getMessage() + "\"}");
            } catch (Exception ex) {
                logger.error("返回错误信息失败", ex);
            }
        }
    }

    /**
     * 获取采购单-sku维度详细信息
     */
    @PreAuthorize("@ss.hasPermi('data:purchaseOrderSkuDim:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(purchaseOrderSkuDimService.selectPurchaseOrderSkuDimById(id));
    }

    /**
     * 新增采购单-sku维度
     */
    @PreAuthorize("@ss.hasPermi('data:purchaseOrderSkuDim:add')")
    @Log(title = "采购单-sku维度", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PurchaseOrderSkuDim purchaseOrderSkuDim)
    {
        return toAjax(purchaseOrderSkuDimService.insertPurchaseOrderSkuDim(purchaseOrderSkuDim));
    }

    /**
     * 修改采购单-sku维度
     */
    @PreAuthorize("@ss.hasPermi('data:purchaseOrderSkuDim:edit')")
    @Log(title = "采购单-sku维度", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PurchaseOrderSkuDim purchaseOrderSkuDim)
    {
        return toAjax(purchaseOrderSkuDimService.updatePurchaseOrderSkuDim(purchaseOrderSkuDim));
    }

    /**
     * 导入采购单-sku维度数据
     *
     * @param file Excel文件
     * @param updateSupport 是否支持更新（true: 存在则更新，不存在则新增；false: 仅新增，重复记录跳过）
     */
    @PreAuthorize("@ss.hasPermi('data:purchaseOrderSkuDim:import')")
    @Log(title = "采购单-sku维度", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        String result = purchaseOrderSkuDimService.importPurchaseOrderSkuDim(file, updateSupport);
        return success(result);
    }

    /**
     * 根据采购单号和系统SKU查询采购单-sku维度
     */
    @PreAuthorize("@ss.hasPermi('data:purchaseOrderSkuDim:query')")
    @GetMapping("/getByOrderAndSku")
    public AjaxResult getByOrderAndSku(String purchaseOrderNo, String systemSku)
    {
        PurchaseOrderSkuDim purchaseOrderSkuDim = purchaseOrderSkuDimService.selectByPurchaseOrderNoAndSystemSku(purchaseOrderNo, systemSku);
        return success(purchaseOrderSkuDim);
    }

    /**
     * 下载采购单-sku维度导入模板
     */
    @PreAuthorize("@ss.hasPermi('data:purchaseOrderSkuDim:import')")
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response)
    {
        ExcelUtil<PurchaseOrderSkuDim> util = new ExcelUtil<PurchaseOrderSkuDim>(PurchaseOrderSkuDim.class);
        util.importTemplateExcel(response, "采购单-sku维度数据");
    }

    /**
     * 删除采购单-sku维度
     */
    @PreAuthorize("@ss.hasPermi('data:purchaseOrderSkuDim:remove')")
    @Log(title = "采购单-sku维度", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(purchaseOrderSkuDimService.deletePurchaseOrderSkuDimByIds(ids));
    }
}
