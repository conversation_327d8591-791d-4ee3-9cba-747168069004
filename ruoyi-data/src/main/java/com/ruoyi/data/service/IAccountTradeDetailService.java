package com.ruoyi.data.service;

import java.util.List;
import java.io.InputStream;
import com.ruoyi.data.domain.AccountTradeDetail;
import com.ruoyi.data.util.PageQueryFunction;

/**
 * 账户交易明细Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-05
 */
public interface IAccountTradeDetailService 
{
    /**
     * 查询账户交易明细
     *
     * @param id 账户交易明细主键
     * @return 账户交易明细
     */
    public AccountTradeDetail selectAccountTradeDetailById(Long id);

    /**
     * 根据交易流水号查询账户交易明细
     *
     * @param tradeSerial 交易流水号
     * @return 账户交易明细
     */
    public AccountTradeDetail selectAccountTradeDetailByTradeSerial(String tradeSerial);

    /**
     * 查询账户交易明细列表
     * 
     * @param accountTradeDetail 账户交易明细
     * @return 账户交易明细集合
     */
    public List<AccountTradeDetail> selectAccountTradeDetailList(AccountTradeDetail accountTradeDetail);

    /**
     * 分页查询账户交易明细列表（用于流式导出全量数据）
     * 
     * @param accountTradeDetail 查询条件
     * @param pageNum 页码
     * @param pageSize 页大小
     * @return 账户交易明细集合
     */
    public List<AccountTradeDetail> selectAccountTradeDetailListByPage(AccountTradeDetail accountTradeDetail, int pageNum, int pageSize);

    /**
     * 统计账户交易明细总数（用于导出进度计算）
     * 
     * @param accountTradeDetail 查询条件
     * @return 总数
     */
    public long countAccountTradeDetail(AccountTradeDetail accountTradeDetail);

    /**
     * 创建分页查询函数（用于流式导出）
     * 
     * @param accountTradeDetail 查询条件
     * @return 分页查询函数
     */
    public PageQueryFunction<AccountTradeDetail> createPageQueryFunction(AccountTradeDetail accountTradeDetail);

    /**
     * 新增账户交易明细
     * 
     * @param accountTradeDetail 账户交易明细
     * @return 结果
     */
    public int insertAccountTradeDetail(AccountTradeDetail accountTradeDetail);

    /**
     * 修改账户交易明细
     * 
     * @param accountTradeDetail 账户交易明细
     * @return 结果
     */
    public int updateAccountTradeDetail(AccountTradeDetail accountTradeDetail);

    /**
     * 批量删除账户交易明细
     * 
     * @param ids 需要删除的账户交易明细主键集合
     * @return 结果
     */
    public int deleteAccountTradeDetailByIds(Long[] ids);

    /**
     * 删除账户交易明细信息
     * 
     * @param id 账户交易明细主键
     * @return 结果
     */
    public int deleteAccountTradeDetailById(Long id);

    /**
     * 导入账户交易明细数据
     *
     * @param accountTradeDetailList 账户交易明细数据列表
     * @return 结果
     * @throws Exception 导入异常
     */
    public String importAccountTradeDetail(List<AccountTradeDetail> accountTradeDetailList) throws Exception;

    /**
     * 流式导入账户交易明细数据（用于大文件）
     *
     * @param inputStream Excel文件输入流
     * @param fileName 文件名
     * @return 结果
     * @throws Exception 导入异常
     */
    public String streamImportAccountTradeDetail(InputStream inputStream, String fileName) throws Exception;

    /**
     * 更新店铺数据
     * 查询flow_status为0或1的数据，分批次调用处理服务
     * 防重复执行：定时任务和手动触发共用同一个标识变量
     *
     * @return 处理结果信息
     */
    public String updateShopData();
}
