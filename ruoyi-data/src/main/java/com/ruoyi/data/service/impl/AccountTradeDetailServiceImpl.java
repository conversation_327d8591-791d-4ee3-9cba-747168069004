package com.ruoyi.data.service.impl;

import java.io.InputStream;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.Date;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.data.mapper.AccountTradeDetailMapper;
import com.ruoyi.data.domain.AccountTradeDetail;
import com.ruoyi.data.service.IAccountTradeDetailService;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.poi.ExcelStreamUtil;
import com.ruoyi.data.util.BatchImportUtil;
import com.ruoyi.data.util.BatchImportUtil.BatchImportResult;
import com.ruoyi.data.util.PageQueryFunction;
import com.ruoyi.data.strategy.service.AccountTradeProcessService;
import com.ruoyi.data.strategy.dto.BatchProcessResult;
import com.ruoyi.data.constants.TradeTypeConstants;
import com.ruoyi.data.strategy.enums.FlowStatusEnum;
import org.springframework.transaction.annotation.Transactional;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 账户交易明细Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-05
 */
@Service
public class AccountTradeDetailServiceImpl implements IAccountTradeDetailService
{
    private static final Logger log = LoggerFactory.getLogger(AccountTradeDetailServiceImpl.class);

    /**
     * 防重复执行标志 - 用于updateShopData方法（定时任务和手动触发共用）
     */
    private static final AtomicBoolean isUpdateShopDataRunning = new AtomicBoolean(false);

    @Autowired
    private AccountTradeDetailMapper accountTradeDetailMapper;

    @Autowired
    private BatchImportUtil batchImportUtil;

    @Autowired
    private AccountTradeProcessService accountTradeProcessService;

    /**
     * 查询账户交易明细
     * 
     * @param id 账户交易明细主键
     * @return 账户交易明细
     */
    @Override
    public AccountTradeDetail selectAccountTradeDetailById(Long id)
    {
        return accountTradeDetailMapper.selectAccountTradeDetailById(id);
    }

    /**
     * 查询账户交易明细列表
     * 
     * @param accountTradeDetail 账户交易明细
     * @return 账户交易明细
     */
    @Override
    public List<AccountTradeDetail> selectAccountTradeDetailList(AccountTradeDetail accountTradeDetail)
    {
        return accountTradeDetailMapper.selectAccountTradeDetailList(accountTradeDetail);
    }

    /**
     * 分页查询账户交易明细列表（用于流式导出全量数据）
     * 性能优化版：直接使用LIMIT/OFFSET，避免PageHelper的额外开销
     * 
     * @param accountTradeDetail 查询条件
     * @param pageNum 页码（从1开始）
     * @param pageSize 页大小
     * @return 账户交易明细集合
     */
    @Override
    public List<AccountTradeDetail> selectAccountTradeDetailListByPage(AccountTradeDetail accountTradeDetail, int pageNum, int pageSize)
    {
        // 计算偏移量（MySQL OFFSET从0开始）
        int offset = (pageNum - 1) * pageSize;
        
        // 设置分页参数到查询对象中
        Map<String, Object> params = new HashMap<>();
        if (accountTradeDetail.getParams() != null) {
            params.putAll(accountTradeDetail.getParams());
        }
        params.put("pageSize", pageSize);
        params.put("offset", offset);
        accountTradeDetail.setParams(params);
        
        log.debug("🔍 执行分页查询：pageNum={}, pageSize={}, offset={}", pageNum, pageSize, offset);
        
        long startTime = System.currentTimeMillis();
        List<AccountTradeDetail> result = accountTradeDetailMapper.selectAccountTradeDetailListByPage(accountTradeDetail);
        long endTime = System.currentTimeMillis();
        
        log.debug("📊 分页查询完成：耗时{}ms，返回{}条记录", (endTime - startTime), result.size());
        
        return result;
    }

    /**
     * 统计账户交易明细总数（用于导出进度计算）
     * 
     * @param accountTradeDetail 查询条件
     * @return 总数
     */
    @Override
    public long countAccountTradeDetail(AccountTradeDetail accountTradeDetail)
    {
        return accountTradeDetailMapper.countAccountTradeDetail(accountTradeDetail);
    }

    /**
     * 创建分页查询函数（用于流式导出）
     * 
     * @param accountTradeDetail 查询条件
     * @return 分页查询函数
     */
    @Override
    public PageQueryFunction<AccountTradeDetail> createPageQueryFunction(AccountTradeDetail accountTradeDetail)
    {
        return PageQueryFunction.of((pageNum, pageSize) ->
            selectAccountTradeDetailListByPage(accountTradeDetail, pageNum, pageSize)
        );
    }

    /**
     * 新增账户交易明细
     * 
     * @param accountTradeDetail 账户交易明细
     * @return 结果
     */
    @Override
    public int insertAccountTradeDetail(AccountTradeDetail accountTradeDetail)
    {
        // 1. 参数校验
        if (accountTradeDetail == null) {
            throw new ServiceException("账户交易明细对象不能为空");
        }
        if (accountTradeDetail.getTradeSerial() == null || accountTradeDetail.getTradeSerial().trim().isEmpty()) {
            throw new ServiceException("交易流水号不能为空");
        }
        
        // 2. 数据清理
        accountTradeDetail.setTradeSerial(accountTradeDetail.getTradeSerial().trim());
        
        // 3. 唯一性校验
        AccountTradeDetail existing = accountTradeDetailMapper.selectAccountTradeDetailByTradeSerial(
            accountTradeDetail.getTradeSerial()
        );
        if (existing != null) {
            throw new ServiceException("交易流水号已存在：" + accountTradeDetail.getTradeSerial());
        }
        
        // 4. 处理店铺字段和流水状态
        boolean hasShopInfo = StringUtils.isNotEmpty(accountTradeDetail.getShop());
        Long flowStatus;
        if (hasShopInfo) {
            // 如果有店铺信息，设置为成功状态
            flowStatus = 2L;
            log.debug("新增记录检测到店铺信息: {}, 设置流水状态为成功", accountTradeDetail.getShop());
        } else {
            // 根据交易类型设置flowStatus
            flowStatus = determineFlowStatusByTradeType(accountTradeDetail.getTradeType());
        }
        accountTradeDetail.setFlowStatus(flowStatus);

        // 5. 设置操作时间（新增功能，支持操作追踪）
        accountTradeDetail.setOperationCreateTime(new Date());
        accountTradeDetail.setOperationUpdateTime(new Date());

        // 6. 设置执行日志，包含店铺信息来源
        if (accountTradeDetail.getExecutionLog() == null) {
            String createLog;
            if (hasShopInfo) {
                createLog = String.format("[%s] 手动新增 - 店铺: %s (手动填写店铺字段)",
                    java.time.LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")),
                    accountTradeDetail.getShop());
            } else {
                createLog = String.format("[%s] 手动新增 - 交易流水: %s",
                    java.time.LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")),
                    accountTradeDetail.getTradeSerial());
            }
            accountTradeDetail.setExecutionLog(createLog);
        }

        // 7. 设置默认日志分类（如果没有设置）
        if (accountTradeDetail.getLogCategories() == null || accountTradeDetail.getLogCategories().trim().isEmpty()) {
            accountTradeDetail.setLogCategories("NORMAL");
        }
        
        return accountTradeDetailMapper.insertAccountTradeDetail(accountTradeDetail);
    }

    /**
     * 修改账户交易明细
     * 
     * @param accountTradeDetail 账户交易明细
     * @return 结果
     */
    @Override
    public int updateAccountTradeDetail(AccountTradeDetail accountTradeDetail)
    {
        // 1. 处理子单号末尾的"/"
        processSubOrderCode(accountTradeDetail, "更新");
        
        // 2. 处理店铺字段和流水状态
        boolean hasShopInfo = StringUtils.isNotEmpty(accountTradeDetail.getShop());
        if (hasShopInfo && (accountTradeDetail.getFlowStatus() == null || accountTradeDetail.getFlowStatus() != 2L)) {
            // 如果有店铺信息且当前状态不是成功状态，则更新为成功状态
            accountTradeDetail.setFlowStatus(2L);
            log.debug("更新记录检测到店铺信息: {}, 设置流水状态为成功", accountTradeDetail.getShop());
        }

        // 3. 设置操作更新时间
        accountTradeDetail.setOperationUpdateTime(new Date());

        // 4. 如果没有设置执行日志，添加更新日志
        if (accountTradeDetail.getExecutionLog() == null) {
            String updateLog;
            if (hasShopInfo) {
                updateLog = String.format("[%s] 数据更新 - 手动修改, 店铺: %s (手动填写店铺字段)",
                    java.time.LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")),
                    accountTradeDetail.getShop());
            } else {
                updateLog = String.format("[%s] 数据更新 - 手动修改",
                    java.time.LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            }
            accountTradeDetail.setExecutionLog(updateLog);
        }
        
        return accountTradeDetailMapper.updateAccountTradeDetail(accountTradeDetail);
    }

    /**
     * 删除账户交易明细（增强版，包含ID校验）
     * 
     * @param id 账户交易明细主键
     * @return 结果
     */
    @Override
    public int deleteAccountTradeDetailById(Long id)
    {
        // 1. 参数校验
        if (id == null || id <= 0) {
            throw new ServiceException("删除ID无效");
        }
        
        // 2. ID存在性校验
        AccountTradeDetail existing = accountTradeDetailMapper.selectAccountTradeDetailById(id);
        if (existing == null) {
            throw new ServiceException("要删除的账户交易明细不存在，ID：" + id);
        }
        
        // 3. 执行删除
        return accountTradeDetailMapper.deleteAccountTradeDetailById(id);
    }

    /**
     * 批量删除账户交易明细（增强版，包含ID校验）
     * 
     * @param ids 需要删除的账户交易明细主键
     * @return 结果
     */
    @Override
    public int deleteAccountTradeDetailByIds(Long[] ids)
    {
        // 1. 参数校验
        if (ids == null || ids.length == 0) {
            throw new ServiceException("删除ID列表不能为空");
        }
        
        // 2. 校验每个ID的有效性
        for (Long id : ids) {
            if (id == null || id <= 0) {
                throw new ServiceException("删除ID无效：" + id);
            }
        }
        
        // 3. 执行批量删除
        return accountTradeDetailMapper.deleteAccountTradeDetailByIds(ids);
    }

    /**
     * 导入账户交易明细数据（批量优化版）
     * 
     * @param accountTradeDetailList 账户交易明细数据列表
     * @return 结果
     */
    @Override
    public String importAccountTradeDetail(List<AccountTradeDetail> accountTradeDetailList) throws Exception
    {
        if (StringUtils.isNull(accountTradeDetailList) || accountTradeDetailList.size() == 0)
        {
            throw new ServiceException("导入数据不能为空！");
        }

        // 数据预处理和验证
        List<AccountTradeDetail> validDataList = new ArrayList<>();
        List<String> tradeSerials = new ArrayList<>();
        List<String> errorMessages = new ArrayList<>();

        for (AccountTradeDetail accountTradeDetail : accountTradeDetailList)
        {
            try
            {
                // 数据验证和清理
                String validationError = validateAndCleanData(accountTradeDetail);
                if (validationError != null)
                {
                    errorMessages.add(validationError);
                    continue;
                }
                
                validDataList.add(accountTradeDetail);
                tradeSerials.add(accountTradeDetail.getTradeSerial());
            }
            catch (Exception e)
            {
                String tradeSerial = accountTradeDetail != null && StringUtils.isNotEmpty(accountTradeDetail.getTradeSerial()) 
                    ? accountTradeDetail.getTradeSerial() : "未知";
                errorMessages.add("账户交易明细 " + tradeSerial + " 数据验证异常：" + e.getMessage());
                log.error("数据验证异常：tradeSerial={}", tradeSerial, e);
            }
        }

        // 如果有验证错误，构建错误消息并抛出异常
        if (!errorMessages.isEmpty())
        {
            StringBuilder failureMsg = new StringBuilder();
            failureMsg.append("很抱歉，导入失败！共 ").append(errorMessages.size()).append(" 条数据格式不正确，错误如下：");
            for (int i = 0; i < errorMessages.size(); i++)
            {
                failureMsg.append("<br/>").append(i + 1).append("、").append(errorMessages.get(i));
            }
            throw new ServiceException(failureMsg.toString());
        }

        if (validDataList.isEmpty())
        {
            throw new ServiceException("没有有效的数据可以导入！");
        }

        // 处理Excel内部重复数据（tradeSerial重复时，后面的覆盖前面的）
        Map<String, AccountTradeDetail> deduplicatedMap = new LinkedHashMap<>();
        int duplicateCount = 0;
        
        for (AccountTradeDetail detail : validDataList)
        {
            String tradeSerial = detail.getTradeSerial();
            if (deduplicatedMap.containsKey(tradeSerial))
            {
                duplicateCount++;
                log.info("发现Excel内部重复数据，交易流水：{}，使用最新数据覆盖旧数据", tradeSerial);
            }
            deduplicatedMap.put(tradeSerial, detail);
        }
        
        // 更新处理后的数据列表
        validDataList = new ArrayList<>(deduplicatedMap.values());
        tradeSerials = new ArrayList<>(deduplicatedMap.keySet());
        
        if (duplicateCount > 0)
        {
            log.info("Excel内部去重完成，发现并处理了 {} 条重复数据，实际处理数据 {} 条", 
                    duplicateCount, validDataList.size());
        }

        // 批量查询已存在的记录
        Map<String, AccountTradeDetail> existingRecordsMap = new HashMap<>();
        if (!tradeSerials.isEmpty())
        {
            List<AccountTradeDetail> existingRecords = accountTradeDetailMapper.selectAccountTradeDetailByTradeSerials(tradeSerials);
            for (AccountTradeDetail record : existingRecords)
            {
                existingRecordsMap.put(record.getTradeSerial(), record);
            }
        }

        // 分离新增和更新数据
        List<AccountTradeDetail> insertList = new ArrayList<>();
        List<AccountTradeDetail> updateList = new ArrayList<>();

        for (AccountTradeDetail detail : validDataList)
        {
            // 根据交易类型设置flowStatus
            Long flowStatus = determineFlowStatusByTradeType(detail.getTradeType());

            // 检查是否有店铺信息，如果有则设置为成功状态
            boolean hasShopInfo = StringUtils.isNotEmpty(detail.getShop());
            if (hasShopInfo) {
                flowStatus = 2L; // 设置为成功状态
                log.debug("检测到店铺信息: {}, 设置流水状态为成功", detail.getShop());
            }

            detail.setFlowStatus(flowStatus);

            // 设置操作时间和初始执行日志
            Date now = new Date();

            AccountTradeDetail existingRecord = existingRecordsMap.get(detail.getTradeSerial());
            if (existingRecord != null)
            {
                // 检查是否应该更新已存在的记录
                boolean shouldUpdate = shouldUpdateExistingRecord(existingRecord, detail, hasShopInfo);

                if (shouldUpdate) {
                    // 设置ID用于更新
                    detail.setId(existingRecord.getId());
                    detail.setOperationUpdateTime(now);
                    // 保持原有的创建时间
                    detail.setOperationCreateTime(existingRecord.getOperationCreateTime());

                    // 生成更新日志
                    String importUpdateLog = generateUpdateLog(existingRecord, detail, hasShopInfo);
                    detail.setExecutionLog(importUpdateLog);
                    updateList.add(detail);
                } else {
                    // 跳过更新，记录跳过原因
                    log.info("跳过更新交易流水 {}: 已存在成功处理的记录且有店铺信息，导入数据无店铺信息",
                            detail.getTradeSerial());
                }
            }
            else
            {
                detail.setOperationCreateTime(now);
                detail.setOperationUpdateTime(now);

                // 添加导入创建日志，包含店铺信息
                String importCreateLog;
                if (hasShopInfo) {
                    importCreateLog = String.format("[%s] 导入创建 - 文件: %s, 交易流水: %s, 店铺: %s (通过Excel表导入更新)",
                        java.time.LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")),
                        "Excel导入",
                        detail.getTradeSerial(),
                        detail.getShop());
                } else {
                    importCreateLog = String.format("[%s] 导入创建 - 文件: %s, 交易流水: %s",
                        java.time.LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")),
                        "Excel导入",
                        detail.getTradeSerial());
                }
                detail.setExecutionLog(importCreateLog);
                insertList.add(detail);
            }
        }

        // 执行批量操作
        int insertCount = 0;
        int updateCount = 0;
        final int finalDuplicateCount = duplicateCount; // 保存用于在try块中使用
        
        try
        {
            // 批量插入新记录
            if (!insertList.isEmpty())
            {
                BatchImportResult insertResult = batchImportUtil.batchImport(
                    insertList,
                    new java.util.function.Function<List<AccountTradeDetail>, Integer>() {
                        @Override
                        public Integer apply(List<AccountTradeDetail> list) {
                            return accountTradeDetailMapper.batchInsertAccountTradeDetail(list);
                        }
                    },
                    new java.util.function.Function<AccountTradeDetail, String>() {
                        @Override
                        public String apply(AccountTradeDetail data) {
                            return "交易流水 " + data.getTradeSerial();
                        }
                    }
                );
                
                if (!insertResult.isSuccess())
                {
                    throw new ServiceException("批量插入失败：" + insertResult.getMessage());
                }
                insertCount = insertList.size();
            }

            // 批量更新已存在记录
            if (!updateList.isEmpty())
            {
                BatchImportResult updateResult = batchImportUtil.batchImport(
                    updateList,
                    new java.util.function.Function<List<AccountTradeDetail>, Integer>() {
                        @Override
                        public Integer apply(List<AccountTradeDetail> list) {
                            return accountTradeDetailMapper.batchUpdateAccountTradeDetail(list);
                        }
                    },
                    new java.util.function.Function<AccountTradeDetail, String>() {
                        @Override
                        public String apply(AccountTradeDetail data) {
                            return "交易流水 " + data.getTradeSerial();
                        }
                    }
                );
                
                if (!updateResult.isSuccess())
                {
                    throw new ServiceException("批量更新失败：" + updateResult.getMessage());
                }
                updateCount = updateList.size();
            }

            String resultMsg;
            if (finalDuplicateCount > 0) {
                resultMsg = String.format("恭喜您，数据已全部导入成功！共处理 %d 条有效数据（Excel内部去重 %d 条），其中新增 %d 条，更新 %d 条",
                        validDataList.size(), finalDuplicateCount, insertCount, updateCount);
            } else {
                resultMsg = String.format("恭喜您，数据已全部导入成功！共 %d 条，其中新增 %d 条，更新 %d 条",
                        validDataList.size(), insertCount, updateCount);
            }
            
            log.info("账户交易明细批量导入完成：总计{}条，Excel内部去重{}条，新增{}条，更新{}条", 
                    validDataList.size(), finalDuplicateCount, insertCount, updateCount);
            
            return resultMsg;
        }
        catch (Exception e)
        {
            log.error("账户交易明细批量导入失败", e);
            if (e instanceof ServiceException)
            {
                throw e;
            }
            throw new ServiceException("批量导入失败，请联系管理员");
        }
    }

    /**
     * 验证和清理数据
     * 
     * @param accountTradeDetail 账户交易明细
     * @return 验证错误信息，如果验证通过返回null
     */
    private String validateAndCleanData(AccountTradeDetail accountTradeDetail)
    {
        // 校验交易流水是否存在
        if (StringUtils.isEmpty(accountTradeDetail.getTradeSerial()))
        {
            return "账户交易明细缺少必要字段: 交易流水不可为空！";
        }

        // 清理字符串字段
        accountTradeDetail.setTradeSerial(StringUtils.trimToEmpty(accountTradeDetail.getTradeSerial()));
        accountTradeDetail.setPayAccount(StringUtils.trimToEmpty(accountTradeDetail.getPayAccount()));
        accountTradeDetail.setIncomeType(StringUtils.trimToEmpty(accountTradeDetail.getIncomeType()));
        accountTradeDetail.setTradeType(StringUtils.trimToEmpty(accountTradeDetail.getTradeType()));
        accountTradeDetail.setTradeStatus(StringUtils.trimToEmpty(accountTradeDetail.getTradeStatus()));
        accountTradeDetail.setTradeAmount(StringUtils.trimToEmpty(accountTradeDetail.getTradeAmount()));
        accountTradeDetail.setCurrency(StringUtils.trimToEmpty(accountTradeDetail.getCurrency()));
        accountTradeDetail.setAvailableAmount(StringUtils.trimToEmpty(accountTradeDetail.getAvailableAmount()));
        accountTradeDetail.setFrozenAmount(StringUtils.trimToEmpty(accountTradeDetail.getFrozenAmount()));
        accountTradeDetail.setAccountAmount(StringUtils.trimToEmpty(accountTradeDetail.getAccountAmount()));
        accountTradeDetail.setPendingAmount(StringUtils.trimToEmpty(accountTradeDetail.getPendingAmount()));
        accountTradeDetail.setDeposit(StringUtils.trimToEmpty(accountTradeDetail.getDeposit()));
        accountTradeDetail.setOrderCode(StringUtils.trimToEmpty(accountTradeDetail.getOrderCode()));
        
        // 先清理子单号字段
        accountTradeDetail.setSubOrderCode(StringUtils.trimToEmpty(accountTradeDetail.getSubOrderCode()));
        // 处理子单号末尾的"/"
        processSubOrderCode(accountTradeDetail, "导入");
        
        accountTradeDetail.setSubOrderType(StringUtils.trimToEmpty(accountTradeDetail.getSubOrderType()));
        accountTradeDetail.setOrderNo(StringUtils.trimToEmpty(accountTradeDetail.getOrderNo()));
        accountTradeDetail.setShop(StringUtils.trimToEmpty(accountTradeDetail.getShop()));

        // 校验交易金额格式
        String tradeAmountStr = accountTradeDetail.getTradeAmount();
        if (StringUtils.isNotEmpty(tradeAmountStr))
        {
            if (tradeAmountStr.contains("["))
            {
                // 使用正则提取金额与币种
                Pattern pattern = Pattern.compile("^(\\d+\\.?\\d*|\\d*\\.?\\d+)\\[(\\p{Alpha}+)\\]$");
                Matcher matcher = pattern.matcher(tradeAmountStr);
                if (matcher.find())
                {
                    String amountStr = matcher.group(1);
                    String currency = matcher.group(2);
                    accountTradeDetail.setCurrency(currency);
                    accountTradeDetail.setTradeAmount(amountStr);
                }
                                        else
                        {
                            return "账户交易明细 " + accountTradeDetail.getTradeSerial() + " 交易金额格式非法，请使用格式如：37.618[CNY]";
                        }
            }
            // 如果不包含[],直接使用原金额
        }
        else
        {
            return "账户交易明细 " + accountTradeDetail.getTradeSerial() + " 金额不可为空";
        }

        // 校验子单号类型
        String subOrderCode = accountTradeDetail.getSubOrderCode();
        if (StringUtils.isNotEmpty(subOrderCode))
        {
            if (subOrderCode.endsWith("订单号"))
            {
                accountTradeDetail.setSubOrderType("订单");
            }
            else if (subOrderCode.matches(".*_.*"))
            {
                accountTradeDetail.setSubOrderType("sku");
            }
            else
            {
                accountTradeDetail.setSubOrderType("其他");
            }
        }
        else
        {
            accountTradeDetail.setSubOrderType("未知");
        }

        return null;
    }

    /**
     * 处理子单号末尾的"/"
     *
     * @param accountTradeDetail 账户交易明细
     * @param operation 操作类型（用于日志记录）
     */
    private void processSubOrderCode(AccountTradeDetail accountTradeDetail, String operation)
    {
        String subOrderCode = accountTradeDetail.getSubOrderCode();
        if (StringUtils.isNotEmpty(subOrderCode) && subOrderCode.endsWith("/")) {
            String originalSubOrderCode = subOrderCode;
            subOrderCode = subOrderCode.substring(0, subOrderCode.length() - 1);
            accountTradeDetail.setSubOrderCode(subOrderCode);
            log.debug("{}操作检测到子单号末尾包含'/'，已自动去除：{} -> {}",
                     operation, originalSubOrderCode, subOrderCode);
        }
    }

    /**
     * 根据交易类型确定flowStatus
     * 
     * @param tradeType 交易类型
     * @return flowStatus值
     */
    private Long determineFlowStatusByTradeType(String tradeType)
    {
        if (StringUtils.isEmpty(tradeType)) {
            log.debug("交易类型为空，设置为不支持的交易类型状态");
            return FlowStatusEnum.UNSUPPORTED_TYPE.getCode().longValue();
        }
        
        if (TradeTypeConstants.isSupportedTradeType(tradeType)) {
            log.debug("交易类型 {} 为支持的类型，设置为未处理状态", tradeType);
            return FlowStatusEnum.UNPROCESSED.getCode().longValue();
        } else {
            log.debug("交易类型 {} 为不支持的类型，设置为不支持的交易类型状态", tradeType);
            return FlowStatusEnum.UNSUPPORTED_TYPE.getCode().longValue();
        }
    }

    /**
     * 更新店铺数据
     * 查询flow_status为0、1或4的数据，分批次调用处理服务
     * 包含RPA失败重试机制：当RPA任务创建失败时，重新尝试处理
     * 防重复执行：定时任务和手动触发共用同一个标识变量
     *
     * @return 处理结果信息
     */
    @Override
    public String updateShopData()
    {
        // 检查是否已有更新任务在运行（定时任务和手动触发共用）
        if (!isUpdateShopDataRunning.compareAndSet(false, true)) {
            String msg = "店铺数据更新任务已在运行中，拒绝重复执行";
            log.warn(msg);
            return msg;
        }

        try {
            log.info("🚀 开始更新店铺数据处理（包含RPA重试）");

            // 优化查询：直接在SQL层面过滤flow_status为0、1或4的数据
            List<AccountTradeDetail> pendingList = accountTradeDetailMapper.selectPendingAccountTradeDetails();

            log.info("📊 查询到待处理记录: {} 条（包含未处理、失败、RPA重试）", pendingList.size());

            if (pendingList.isEmpty()) {
                log.info("✅ 没有需要处理的数据");
                return "没有需要处理的数据";
            }

            // 统计不同流程状态的数量
            Map<Long, Long> flowStatusStats = new HashMap<>();
            for (AccountTradeDetail detail : pendingList) {
                Long flowStatus = detail.getFlowStatus();
                flowStatusStats.put(flowStatus, flowStatusStats.getOrDefault(flowStatus, 0L) + 1L);
            }

            // 统计不同交易类型的数量
            Map<String, Long> tradeTypeStats = new HashMap<>();
            for (AccountTradeDetail detail : pendingList) {
                String tradeType = detail.getTradeType();
                tradeTypeStats.put(tradeType, tradeTypeStats.getOrDefault(tradeType, 0L) + 1L);
            }
            
            log.info("📈 待处理数据按流程状态统计: {} (0=未处理, 1=失败, 4=RPA重试)", flowStatusStats);
            log.info("📈 待处理数据按交易类型统计: {}", tradeTypeStats);

            // 分批处理，每批500条
            int batchSize = 500;
            int totalBatches = (pendingList.size() + batchSize - 1) / batchSize;
            int totalSuccess = 0;
            int totalFailure = 0;
            int totalRpa = 0;

            log.info("🔄 开始分批处理，总批次: {}, 每批大小: {}", totalBatches, batchSize);

            for (int i = 0; i < totalBatches; i++) {
                int startIndex = i * batchSize;
                int endIndex = Math.min(startIndex + batchSize, pendingList.size());
                List<AccountTradeDetail> batchList = pendingList.subList(startIndex, endIndex);

                log.info("⚡ 处理第 {}/{} 批，数量: {}", i + 1, totalBatches, batchList.size());

                try {
                    BatchProcessResult batchResult = accountTradeProcessService.batchProcessAccountTradeDetails(batchList);
                    totalSuccess += batchResult.getSuccessCount();
                    totalFailure += batchResult.getFailureCount();
                    totalRpa += batchResult.getRpaCount();
                    
                    log.info("✨ 第 {}/{} 批处理完成 - 成功: {}, 失败: {}, RPA: {}",
                             i + 1, totalBatches, batchResult.getSuccessCount(), 
                             batchResult.getFailureCount(), batchResult.getRpaCount());
                    
                    // 输出失败记录的详细信息（仅前5条）
                    if (batchResult.getFailureRecords() != null && !batchResult.getFailureRecords().isEmpty()) {
                        int showCount = Math.min(5, batchResult.getFailureRecords().size());
                        StringBuilder failureExamples = new StringBuilder();
                        for (int j = 0; j < showCount; j++) {
                            if (j > 0) failureExamples.append(", ");
                            BatchProcessResult.FailureRecord record = batchResult.getFailureRecords().get(j);
                            failureExamples.append("ID:").append(record.getTradeDetailId()).append("→").append(record.getErrorMessage());
                        }
                        log.info("❌ 失败记录示例(前{}条): {}", showCount, failureExamples.toString());
                    }
                } catch (Exception e) {
                    log.error("💥 第 {}/{} 批处理异常: {}", i + 1, totalBatches, e.getMessage(), e);
                    totalFailure += batchList.size();
                }

                // 批次间休息100ms，避免对数据库造成过大压力
                if (i < totalBatches - 1) { // 最后一批不需要休息
                    try {
                        Thread.sleep(100);
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        log.warn("⚠️ 线程被中断，提前结束处理");
                        break;
                    }
                }
            }

            // 🏪 所有批量处理完成后，执行店铺名推导逻辑
            log.info("🏪 开始执行店铺名推导逻辑...");
            int inferredShopCount = processShopNameInferenceBatch();
            if (inferredShopCount > 0) {
                log.info("✅ 店铺名推导完成，成功推导 {} 条记录的店铺名", inferredShopCount);
                totalSuccess += inferredShopCount;
                totalFailure -= inferredShopCount;
            }

            String result = String.format("🎉 处理完成！总计: %d 条，成功: %d 条，失败: %d 条，RPA: %d 条，店铺推导: %d 条",
                                         pendingList.size(), totalSuccess, totalFailure, totalRpa, inferredShopCount);
            log.info(result);

            // 输出最终统计信息
            if (totalFailure > 0) {
                log.warn("⚠️ 注意：有 {} 条记录处理失败，请检查日志查看详细原因", totalFailure);
            }
            if (totalRpa > 0) {
                log.info("🤖 有 {} 条记录已提交RPA处理，请关注RPA任务执行状态", totalRpa);
            }

            return result;

        } catch (Exception e) {
            String errorMsg = "💥 更新店铺数据异常: " + e.getMessage();
            log.error(errorMsg, e);
            return errorMsg;
        } finally {
            // 无论成功还是异常，都要释放锁
            isUpdateShopDataRunning.set(false);
            log.info("🔓 店铺数据更新任务完成，释放执行锁");
        }
    }

    /**
     * 流式导入账户交易明细数据（用于大文件）
     *
     * @param inputStream Excel文件输入流
     * @param fileName 文件名
     * @return 结果
     * @throws Exception 导入异常
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String streamImportAccountTradeDetail(InputStream inputStream, String fileName) throws Exception
    {
        log.info("开始流式导入账户交易明细数据，文件名：{}", fileName);
        
        // 判断文件类型
        if (!fileName.toLowerCase().endsWith(".xlsx")) {
            throw new ServiceException("流式导入只支持.xlsx格式的Excel文件");
        }
        
        // 创建流式处理工具
        ExcelStreamUtil<AccountTradeDetail> streamUtil = new ExcelStreamUtil<>(AccountTradeDetail.class);
        
        // 统计信息
        AtomicInteger totalCount = new AtomicInteger(0);
        AtomicInteger insertCount = new AtomicInteger(0);
        AtomicInteger updateCount = new AtomicInteger(0);
        AtomicInteger errorCount = new AtomicInteger(0);
        AtomicInteger duplicateCount = new AtomicInteger(0);
        
        // 批次大小
        int batchSize = 500;
        
        try {
            // 流式读取Excel，每批次处理一次
            int processedRows = streamUtil.streamImportExcel(inputStream, 0, batchList -> {
                log.info("处理批次数据，数量：{}", batchList.size());
                
                // 数据预处理和验证
                List<AccountTradeDetail> validDataList = new ArrayList<>();
                List<String> tradeSerials = new ArrayList<>();
                Map<String, AccountTradeDetail> deduplicatedMap = new LinkedHashMap<>();
                
                for (AccountTradeDetail accountTradeDetail : batchList) {
                    try {
                        // 数据验证和清理
                        String validationError = validateAndCleanData(accountTradeDetail);
                        if (validationError != null) {
                            log.warn("数据验证失败：{}", validationError);
                            errorCount.incrementAndGet();
                            continue;
                        }
                        
                        // 批次内去重
                        String tradeSerial = accountTradeDetail.getTradeSerial();
                        if (deduplicatedMap.containsKey(tradeSerial)) {
                            duplicateCount.incrementAndGet();
                            log.debug("批次内发现重复数据，交易流水：{}", tradeSerial);
                        }
                        deduplicatedMap.put(tradeSerial, accountTradeDetail);
                    } catch (Exception e) {
                        log.error("处理数据异常：{}", e.getMessage());
                        errorCount.incrementAndGet();
                    }
                }
                
                validDataList = new ArrayList<>(deduplicatedMap.values());
                tradeSerials = new ArrayList<>(deduplicatedMap.keySet());
                
                if (validDataList.isEmpty()) {
                    return;
                }
                
                // 批量查询已存在的记录
                Map<String, AccountTradeDetail> existingRecordsMap = new HashMap<>();
                if (!tradeSerials.isEmpty()) {
                    List<AccountTradeDetail> existingRecords = accountTradeDetailMapper.selectAccountTradeDetailByTradeSerials(tradeSerials);
                    for (AccountTradeDetail record : existingRecords) {
                        existingRecordsMap.put(record.getTradeSerial(), record);
                    }
                }
                
                // 分离新增和更新数据
                List<AccountTradeDetail> insertList = new ArrayList<>();
                List<AccountTradeDetail> updateList = new ArrayList<>();
                
                for (AccountTradeDetail detail : validDataList) {
                    // 根据交易类型设置flowStatus
                    Long flowStatus = determineFlowStatusByTradeType(detail.getTradeType());

                    // 检查是否有店铺信息，如果有则设置为成功状态
                    boolean hasShopInfo = StringUtils.isNotEmpty(detail.getShop());
                    if (hasShopInfo) {
                        flowStatus = 2L; // 设置为成功状态
                        log.debug("流式导入检测到店铺信息: {}, 设置流水状态为成功", detail.getShop());
                    }

                    detail.setFlowStatus(flowStatus);

                    // 设置操作时间和初始执行日志
                    Date now = new Date();

                    AccountTradeDetail existingRecord = existingRecordsMap.get(detail.getTradeSerial());
                    if (existingRecord != null) {
                        // 设置ID用于更新
                        detail.setId(existingRecord.getId());
                        detail.setOperationUpdateTime(now);
                        // 保持原有的创建时间
                        detail.setOperationCreateTime(existingRecord.getOperationCreateTime());

                        // 添加导入更新日志，包含店铺信息
                        String importUpdateLog;
                        if (hasShopInfo) {
                            importUpdateLog = String.format("[%s] 流式导入更新 - 文件: %s, 店铺: %s (通过Excel表导入更新)",
                                java.time.LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")),
                                fileName,
                                detail.getShop());
                        } else {
                            importUpdateLog = String.format("[%s] 流式导入更新 - 文件: %s",
                                java.time.LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")),
                                fileName);
                        }
                        detail.setExecutionLog(importUpdateLog);
                        updateList.add(detail);
                    } else {
                        detail.setOperationCreateTime(now);
                        detail.setOperationUpdateTime(now);

                        // 添加导入创建日志，包含店铺信息
                        String importCreateLog;
                        if (hasShopInfo) {
                            importCreateLog = String.format("[%s] 流式导入创建 - 文件: %s, 交易流水: %s, 店铺: %s (通过Excel表导入更新)",
                                java.time.LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")),
                                fileName,
                                detail.getTradeSerial(),
                                detail.getShop());
                        } else {
                            importCreateLog = String.format("[%s] 流式导入创建 - 文件: %s, 交易流水: %s",
                                java.time.LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")),
                                fileName,
                                detail.getTradeSerial());
                        }
                        detail.setExecutionLog(importCreateLog);
                        insertList.add(detail);
                    }
                }
                
                // 执行批量操作
                try {
                    // 批量插入新记录
                    if (!insertList.isEmpty()) {
                        BatchImportResult insertResult = batchImportUtil.batchImport(
                            insertList,
                            new java.util.function.Function<List<AccountTradeDetail>, Integer>() {
                                @Override
                                public Integer apply(List<AccountTradeDetail> list) {
                                    return accountTradeDetailMapper.batchInsertAccountTradeDetail(list);
                                }
                            },
                            new java.util.function.Function<AccountTradeDetail, String>() {
                                @Override
                                public String apply(AccountTradeDetail data) {
                                    return "交易流水 " + data.getTradeSerial();
                                }
                            }
                        );
                        
                        if (insertResult.isSuccess()) {
                            insertCount.addAndGet(insertList.size());
                        } else {
                            log.error("批量插入失败：{}", insertResult.getMessage());
                            errorCount.addAndGet(insertList.size());
                        }
                    }
                    
                    // 批量更新已存在记录
                    if (!updateList.isEmpty()) {
                        BatchImportResult updateResult = batchImportUtil.batchImport(
                            updateList,
                            new java.util.function.Function<List<AccountTradeDetail>, Integer>() {
                                @Override
                                public Integer apply(List<AccountTradeDetail> list) {
                                    return accountTradeDetailMapper.batchUpdateAccountTradeDetail(list);
                                }
                            },
                            new java.util.function.Function<AccountTradeDetail, String>() {
                                @Override
                                public String apply(AccountTradeDetail data) {
                                    return "交易流水 " + data.getTradeSerial();
                                }
                            }
                        );
                        
                        if (updateResult.isSuccess()) {
                            updateCount.addAndGet(updateList.size());
                        } else {
                            log.error("批量更新失败：{}", updateResult.getMessage());
                            errorCount.addAndGet(updateList.size());
                        }
                    }
                    
                    totalCount.addAndGet(validDataList.size());
                } catch (Exception e) {
                    log.error("批次处理失败：{}", e.getMessage(), e);
                    errorCount.addAndGet(validDataList.size());
                }
            }, batchSize);
            
            // 构建结果消息
            String resultMsg = String.format(
                "流式导入完成！共读取 %d 行数据，成功处理 %d 条（新增 %d 条，更新 %d 条），失败 %d 条",
                processedRows, totalCount.get(), insertCount.get(), updateCount.get(), errorCount.get()
            );
            
            if (duplicateCount.get() > 0) {
                resultMsg += String.format("，去重 %d 条", duplicateCount.get());
            }
            
            log.info(resultMsg);
            return resultMsg;
            
        } catch (Exception e) {
            log.error("流式导入失败：{}", e.getMessage(), e);
            throw new ServiceException("流式导入失败：" + e.getMessage());
        }
    }

    /**
     * 批量店铺名推导处理
     * 筛选出还没有找到店铺名的，并且交易类型是采购单或订单的，并且不是将要进行RPA的记录
     *
     * @return 成功推导店铺名的记录数量
     */
    private int processShopNameInferenceBatch() {
        log.info("🔍 开始批量店铺名推导处理...");

        try {
            // 1. 查询需要推导店铺名的记录
            List<AccountTradeDetail> needInferenceList = selectRecordsNeedShopInference();

            if (needInferenceList.isEmpty()) {
                log.info("📋 没有需要推导店铺名的记录");
                return 0;
            }

            log.info("📋 找到 {} 条需要推导店铺名的记录", needInferenceList.size());

            // 2. 按交易单号分组，批量查询引用数据
            Map<String, List<AccountTradeDetail>> orderCodeGroups = new HashMap<>();
            for (AccountTradeDetail detail : needInferenceList) {
                if (detail.getOrderCode() != null && !detail.getOrderCode().trim().isEmpty()) {
                    String orderCode = detail.getOrderCode();
                    if (!orderCodeGroups.containsKey(orderCode)) {
                        orderCodeGroups.put(orderCode, new ArrayList<AccountTradeDetail>());
                    }
                    orderCodeGroups.get(orderCode).add(detail);
                }
            }

            if (orderCodeGroups.isEmpty()) {
                log.info("📋 没有有效交易单号的记录，跳过推导");
                return 0;
            }

            log.info("📊 按交易单号分组，共 {} 个不同的交易单号", orderCodeGroups.size());

            // 3. 批量查询引用数据并构建映射
            Map<String, AccountTradeDetail> orderCodeToShopMap = buildOrderCodeToShopMapping(orderCodeGroups.keySet());

            if (orderCodeToShopMap.isEmpty()) {
                log.info("📋 没有找到可引用的店铺信息，跳过推导");
                return 0;
            }

            log.info("📊 找到 {} 个交易单号对应的店铺信息", orderCodeToShopMap.size());

            // 4. 批量更新记录
            List<AccountTradeDetail> updateList = new ArrayList<>();

            for (Map.Entry<String, List<AccountTradeDetail>> entry : orderCodeGroups.entrySet()) {
                String orderCode = entry.getKey();
                List<AccountTradeDetail> records = entry.getValue();
                AccountTradeDetail referenceRecord = orderCodeToShopMap.get(orderCode);

                if (referenceRecord != null) {
                    String inferredShop = referenceRecord.getShop();

                    for (AccountTradeDetail record : records) {
                        // 构建更新对象
                        AccountTradeDetail updateRecord = buildShopInferenceUpdateRecord(
                            record.getId(), inferredShop, referenceRecord, orderCode);
                        updateList.add(updateRecord);

                        log.debug("✅ 记录 ID: {} 推导出店铺名: {} (引用记录 ID: {}, 交易单号: {})",
                                 record.getId(), inferredShop, referenceRecord.getId(), orderCode);
                    }
                }
            }

            // 5. 执行批量更新
            if (updateList.isEmpty()) {
                log.info("📋 没有需要更新的记录");
                return 0;
            }

            int successCount = batchUpdateShopInference(updateList);
            log.info("🎯 店铺名推导完成，成功: {} / {}", successCount, needInferenceList.size());

            return successCount;

        } catch (Exception e) {
            log.error("💥 批量店铺名推导异常: {}", e.getMessage(), e);
            return 0;
        }
    }

    /**
     * 查询需要推导店铺名的记录
     *
     * @return 需要推导店铺名的记录列表
     */
    private List<AccountTradeDetail> selectRecordsNeedShopInference() {
        // 构建查询条件
        AccountTradeDetail queryParam = new AccountTradeDetail();
        List<AccountTradeDetail> allRecords = accountTradeDetailMapper.selectAccountTradeDetailList(queryParam);

        // 筛选需要推导的记录
        List<AccountTradeDetail> filteredRecords = new ArrayList<>();
        for (AccountTradeDetail detail : allRecords) {
            // 店铺名为空
            boolean shopEmpty = detail.getShop() == null || detail.getShop().trim().isEmpty();
            // 交易类型是采购单或订单
            boolean validTradeType = "采购单".equals(detail.getTradeType()) || "订单".equals(detail.getTradeType());
            // 不是准备进行RPA的记录（flow_status != 4）
            boolean notRpa = detail.getFlowStatus() == null || !detail.getFlowStatus().equals(4L);
            // 交易单号不为空
            boolean hasOrderCode = detail.getOrderCode() != null && !detail.getOrderCode().trim().isEmpty();

            if (shopEmpty && validTradeType && notRpa && hasOrderCode) {
                filteredRecords.add(detail);
            }
        }
        return filteredRecords;
    }

    /**
     * 构建交易单号到店铺信息的映射
     *
     * @param orderCodes 交易单号集合
     * @return 交易单号到店铺信息的映射
     */
    private Map<String, AccountTradeDetail> buildOrderCodeToShopMapping(java.util.Set<String> orderCodes) {
        Map<String, AccountTradeDetail> mapping = new HashMap<>();
        List<String> supportedTradeTypes = java.util.Arrays.asList("采购单", "订单");

        for (String orderCode : orderCodes) {
            try {
                List<AccountTradeDetail> recordsWithShop = accountTradeDetailMapper
                    .selectRecordsWithShopByOrderCode(orderCode, supportedTradeTypes);

                if (!recordsWithShop.isEmpty()) {
                    mapping.put(orderCode, recordsWithShop.get(0));
                }
            } catch (Exception e) {
                log.warn("⚠️ 查询交易单号 {} 的店铺信息异常: {}", orderCode, e.getMessage());
            }
        }

        return mapping;
    }

    /**
     * 构建店铺推导更新记录
     *
     * @param recordId 当前记录ID
     * @param inferredShop 推导出的店铺名
     * @param referenceRecord 引用记录
     * @param orderCode 交易单号
     * @return 更新记录对象
     */
    private AccountTradeDetail buildShopInferenceUpdateRecord(Long recordId, String inferredShop,
                                                            AccountTradeDetail referenceRecord, String orderCode) {
        // 构建执行日志
        String executionLog = String.format(
            "[%s] 店铺名推导成功 - 推导店铺: %s, 引用数据: ID=%d, 交易流水=%s, 交易单号=%s",
            java.time.LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")),
            inferredShop,
            referenceRecord.getId(),
            referenceRecord.getTradeSerial() != null ? referenceRecord.getTradeSerial() : "无",
            orderCode
        );

        // 构建更新对象
        AccountTradeDetail updateRecord = new AccountTradeDetail();
        updateRecord.setId(recordId);
        updateRecord.setShop(inferredShop);
        updateRecord.setFlowStatus(2L); // 成功状态
        updateRecord.setExecutionLog(executionLog);
        updateRecord.setLogCategories("SHOP_INFERENCE");
        updateRecord.setOperationUpdateTime(new java.util.Date());

        return updateRecord;
    }

    /**
     * 批量更新店铺推导记录
     *
     * @param updateList 更新记录列表
     * @return 成功更新的记录数量
     */
    private int batchUpdateShopInference(List<AccountTradeDetail> updateList) {
        int successCount = 0;
        int batchSize = 100; // 每批100条记录

        try {
            for (int i = 0; i < updateList.size(); i += batchSize) {
                int endIndex = Math.min(i + batchSize, updateList.size());
                List<AccountTradeDetail> batchList = updateList.subList(i, endIndex);

                for (AccountTradeDetail updateRecord : batchList) {
                    try {
                        int updateCount = accountTradeDetailMapper.updateAccountTradeDetail(updateRecord);
                        if (updateCount > 0) {
                            successCount++;
                        } else {
                            log.warn("⚠️ 记录 ID: {} 店铺名推导更新失败，可能记录不存在", updateRecord.getId());
                        }
                    } catch (Exception e) {
                        log.error("💥 更新推导店铺名异常，记录 ID: {}, 错误: {}", updateRecord.getId(), e.getMessage());
                    }
                }

                log.debug("✅ 批量更新第 {} 批，处理记录数: {}", (i / batchSize) + 1, batchList.size());
            }

        } catch (Exception e) {
            log.error("💥 批量更新店铺推导异常: {}", e.getMessage(), e);
        }

        return successCount;
    }
}
