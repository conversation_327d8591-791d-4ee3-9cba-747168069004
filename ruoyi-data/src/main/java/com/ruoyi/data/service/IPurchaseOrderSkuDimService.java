package com.ruoyi.data.service;

import java.util.List;
import com.ruoyi.data.domain.PurchaseOrderSkuDim;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.data.util.PageQueryFunction;

/**
 * 采购单-sku维度Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-26
 */
public interface IPurchaseOrderSkuDimService 
{
    /**
     * 查询采购单-sku维度
     * 
     * @param id 采购单-sku维度主键
     * @return 采购单-sku维度
     */
    public PurchaseOrderSkuDim selectPurchaseOrderSkuDimById(Long id);

    /**
     * 查询采购单-sku维度列表
     * 
     * @param purchaseOrderSkuDim 采购单-sku维度
     * @return 采购单-sku维度集合
     */
    public List<PurchaseOrderSkuDim> selectPurchaseOrderSkuDimList(PurchaseOrderSkuDim purchaseOrderSkuDim);

    /**
     * 新增采购单-sku维度
     * 
     * @param purchaseOrderSkuDim 采购单-sku维度
     * @return 结果
     */
    public int insertPurchaseOrderSkuDim(PurchaseOrderSkuDim purchaseOrderSkuDim);

    /**
     * 修改采购单-sku维度
     * 
     * @param purchaseOrderSkuDim 采购单-sku维度
     * @return 结果
     */
    public int updatePurchaseOrderSkuDim(PurchaseOrderSkuDim purchaseOrderSkuDim);

    /**
     * 批量删除采购单-sku维度
     * 
     * @param ids 需要删除的采购单-sku维度主键集合
     * @return 结果
     */
    public int deletePurchaseOrderSkuDimByIds(Long[] ids);

    /**
     * 删除采购单-sku维度信息
     *
     * @param id 采购单-sku维度主键
     * @return 结果
     */
    public int deletePurchaseOrderSkuDimById(Long id);

    /**
     * 分页查询采购单-sku维度列表（用于流式导出全量数据）
     *
     * @param purchaseOrderSkuDim 查询条件
     * @param pageNum 页码
     * @param pageSize 页大小
     * @return 采购单-sku维度集合
     */
    public List<PurchaseOrderSkuDim> selectPurchaseOrderSkuDimListByPage(PurchaseOrderSkuDim purchaseOrderSkuDim, int pageNum, int pageSize);

    /**
     * 创建分页查询函数（用于流式导出）
     *
     * @param purchaseOrderSkuDim 查询条件
     * @return 分页查询函数
     */
    public PageQueryFunction<PurchaseOrderSkuDim> createPageQueryFunction(PurchaseOrderSkuDim purchaseOrderSkuDim);

    /**
     * 导入采购单-sku维度数据
     *
     * @param file Excel文件
     * @param updateSupport 是否支持更新
     * @return 导入结果
     * @throws Exception 异常
     */
    public String importPurchaseOrderSkuDim(MultipartFile file, boolean updateSupport) throws Exception;

    /**
     * 根据采购单号和系统SKU查询采购单-sku维度
     *
     * @param purchaseOrderNo 采购单号
     * @param systemSku 系统SKU
     * @return 采购单-sku维度
     */
    public PurchaseOrderSkuDim selectByPurchaseOrderNoAndSystemSku(String purchaseOrderNo, String systemSku);

    /**
     * 批量更新或插入采购单-sku维度
     *
     * @param purchaseOrderSkuDimList 采购单-sku维度列表
     * @return 结果
     */
    public int batchUpsertPurchaseOrderSkuDim(List<PurchaseOrderSkuDim> purchaseOrderSkuDimList);
}
