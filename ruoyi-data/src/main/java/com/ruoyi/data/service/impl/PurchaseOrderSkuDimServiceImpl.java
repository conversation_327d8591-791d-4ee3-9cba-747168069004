package com.ruoyi.data.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.data.mapper.PurchaseOrderSkuDimMapper;
import com.ruoyi.data.domain.PurchaseOrderSkuDim;
import com.ruoyi.data.service.IPurchaseOrderSkuDimService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.data.util.BatchImportUtil;
import com.ruoyi.data.util.BatchImportUtil.BatchImportResult;
import com.ruoyi.common.exception.ServiceException;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.data.util.EnhancedExcelUtil;
import com.ruoyi.data.util.PageQueryFunction;
import com.ruoyi.data.util.UniqueConstraintValidator;

/**
 * 采购单-sku维度Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-26
 */
@Service
public class PurchaseOrderSkuDimServiceImpl implements IPurchaseOrderSkuDimService 
{
    @Autowired
    private PurchaseOrderSkuDimMapper purchaseOrderSkuDimMapper;

    @Autowired
    private BatchImportUtil batchImportUtil;

    @Autowired
    private EnhancedExcelUtil enhancedExcelUtil;

    /**
     * 查询采购单-sku维度
     * 
     * @param id 采购单-sku维度主键
     * @return 采购单-sku维度
     */
    @Override
    public PurchaseOrderSkuDim selectPurchaseOrderSkuDimById(Long id)
    {
        return purchaseOrderSkuDimMapper.selectPurchaseOrderSkuDimById(id);
    }

    /**
     * 查询采购单-sku维度列表
     * 
     * @param purchaseOrderSkuDim 采购单-sku维度
     * @return 采购单-sku维度
     */
    @Override
    public List<PurchaseOrderSkuDim> selectPurchaseOrderSkuDimList(PurchaseOrderSkuDim purchaseOrderSkuDim)
    {
        return purchaseOrderSkuDimMapper.selectPurchaseOrderSkuDimList(purchaseOrderSkuDim);
    }

    /**
     * 新增采购单-sku维度
     * 
     * @param purchaseOrderSkuDim 采购单-sku维度
     * @return 结果
     */
    @Override
    public int insertPurchaseOrderSkuDim(PurchaseOrderSkuDim purchaseOrderSkuDim)
    {
        purchaseOrderSkuDim.setCreateTime(DateUtils.getNowDate());
        return purchaseOrderSkuDimMapper.insertPurchaseOrderSkuDim(purchaseOrderSkuDim);
    }

    /**
     * 修改采购单-sku维度
     * 
     * @param purchaseOrderSkuDim 采购单-sku维度
     * @return 结果
     */
    @Override
    public int updatePurchaseOrderSkuDim(PurchaseOrderSkuDim purchaseOrderSkuDim)
    {
        purchaseOrderSkuDim.setUpdateTime(DateUtils.getNowDate());
        return purchaseOrderSkuDimMapper.updatePurchaseOrderSkuDim(purchaseOrderSkuDim);
    }

    /**
     * 批量删除采购单-sku维度
     * 
     * @param ids 需要删除的采购单-sku维度主键
     * @return 结果
     */
    @Override
    public int deletePurchaseOrderSkuDimByIds(Long[] ids)
    {
        return purchaseOrderSkuDimMapper.deletePurchaseOrderSkuDimByIds(ids);
    }

    /**
     * 删除采购单-sku维度信息
     *
     * @param id 采购单-sku维度主键
     * @return 结果
     */
    @Override
    public int deletePurchaseOrderSkuDimById(Long id)
    {
        return purchaseOrderSkuDimMapper.deletePurchaseOrderSkuDimById(id);
    }

    /**
     * 分页查询采购单-sku维度列表（用于流式导出全量数据）
     *
     * @param purchaseOrderSkuDim 查询条件
     * @param pageNum 页码
     * @param pageSize 页大小
     * @return 采购单-sku维度集合
     */
    @Override
    public List<PurchaseOrderSkuDim> selectPurchaseOrderSkuDimListByPage(PurchaseOrderSkuDim purchaseOrderSkuDim, int pageNum, int pageSize)
    {
        // 使用PageHelper进行分页
        com.github.pagehelper.PageHelper.startPage(pageNum, pageSize, false);
        return purchaseOrderSkuDimMapper.selectPurchaseOrderSkuDimListByPage(purchaseOrderSkuDim);
    }

    /**
     * 创建分页查询函数（用于流式导出）
     *
     * @param purchaseOrderSkuDim 查询条件
     * @return 分页查询函数
     */
    @Override
    public PageQueryFunction<PurchaseOrderSkuDim> createPageQueryFunction(PurchaseOrderSkuDim purchaseOrderSkuDim)
    {
        return PageQueryFunction.of((pageNum, pageSize) ->
            selectPurchaseOrderSkuDimListByPage(purchaseOrderSkuDim, pageNum, pageSize)
        );
    }

    /**
     * 导入采购单-sku维度数据
     *
     * @param file Excel文件
     * @param updateSupport 是否支持更新
     * @return 导入结果
     * @throws Exception 异常
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String importPurchaseOrderSkuDim(MultipartFile file, boolean updateSupport) throws Exception
    {
        try {
            // 使用增强的Excel工具类解析文件
            EnhancedExcelUtil.ImportResult<PurchaseOrderSkuDim> importResult = enhancedExcelUtil.importExcel(
                file,
                PurchaseOrderSkuDim.class
            );

            List<PurchaseOrderSkuDim> dataList = importResult.getDataList();

            if (dataList.isEmpty()) {
                throw new ServiceException("导入数据为空，请检查Excel文件内容");
            }

            // 验证唯一性约束
            UniqueConstraintValidator.ValidationResult validationResult =
                UniqueConstraintValidator.validatePurchaseOrderSkuDimUniqueness(dataList);

            if (!validationResult.isValid()) {
                throw new ServiceException("数据验证失败：\n" + validationResult.getMessage());
            }

            // 根据updateSupport参数决定使用插入还是更新插入操作
            BatchImportUtil.BatchImportResult result;
            if (updateSupport) {
                // 支持更新：使用ON DUPLICATE KEY UPDATE语法
                result = batchImportUtil.batchImport(
                    dataList,
                    new java.util.function.Function<List<PurchaseOrderSkuDim>, Integer>() {
                        @Override
                        public Integer apply(List<PurchaseOrderSkuDim> list) {
                            return purchaseOrderSkuDimMapper.batchUpsertPurchaseOrderSkuDim(list);
                        }
                    },
                    new java.util.function.Function<PurchaseOrderSkuDim, String>() {
                        @Override
                        public String apply(PurchaseOrderSkuDim data) {
                            return "采购单号 " + data.getPurchaseOrderNo() + ", 系统SKU " + data.getSystemSku();
                        }
                    }
                );
            } else {
                // 不支持更新：仅插入新记录，重复记录会被跳过
                result = batchImportUtil.batchImport(
                    dataList,
                    new java.util.function.Function<List<PurchaseOrderSkuDim>, Integer>() {
                        @Override
                        public Integer apply(List<PurchaseOrderSkuDim> list) {
                            return purchaseOrderSkuDimMapper.batchInsertPurchaseOrderSkuDim(list);
                        }
                    },
                    new java.util.function.Function<PurchaseOrderSkuDim, String>() {
                        @Override
                        public String apply(PurchaseOrderSkuDim data) {
                            return "采购单号 " + data.getPurchaseOrderNo() + ", 系统SKU " + data.getSystemSku();
                        }
                    }
                );
            }

            if (!result.isSuccess()) {
                throw new ServiceException("数据导入失败：" + result.getMessage() + "。所有数据已回滚，请修正后重新导入。");
            }

            // 构建详细的结果信息
            StringBuilder resultMsg = new StringBuilder();
            resultMsg.append(importResult.getSummary())
                     .append("，成功保存到数据库！")
                     .append("（采用事务保护，确保数据一致性）");

            return resultMsg.toString();

        } catch (Exception e) {
            // 确保抛出异常以触发事务回滚
            throw new ServiceException("导入失败：" + e.getMessage() + "。已回滚所有更改，请检查数据后重新导入。");
        }
    }

    /**
     * 根据采购单号和系统SKU查询采购单-sku维度
     *
     * @param purchaseOrderNo 采购单号
     * @param systemSku 系统SKU
     * @return 采购单-sku维度
     */
    @Override
    public PurchaseOrderSkuDim selectByPurchaseOrderNoAndSystemSku(String purchaseOrderNo, String systemSku)
    {
        return purchaseOrderSkuDimMapper.selectByPurchaseOrderNoAndSystemSku(purchaseOrderNo, systemSku);
    }

    /**
     * 批量更新或插入采购单-sku维度
     *
     * @param purchaseOrderSkuDimList 采购单-sku维度列表
     * @return 结果
     */
    @Override
    public int batchUpsertPurchaseOrderSkuDim(List<PurchaseOrderSkuDim> purchaseOrderSkuDimList)
    {
        return purchaseOrderSkuDimMapper.batchUpsertPurchaseOrderSkuDim(purchaseOrderSkuDimList);
    }
}
