package com.ruoyi.data.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 账户交易明细对象 account_trade_detail
 * 
 * <AUTHOR>
 * @date 2025-06-05
 */
public class AccountTradeDetail extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 自增主键 */
    private Long id;

    /** 交易流水 */
    @Excel(name = "交易流水号")
    private String tradeSerial;

    /** 支付账号 */
    @Excel(name = "支付账号")
    private String payAccount;

    /** 收支类型 */
    @Excel(name = "收支类型")
    private String incomeType;

    /** 交易类型 */
    @Excel(name = "交易类型")
    private String tradeType;

    /** 交易状态/结算类型 */
    @Excel(name = "交易编号")
    private String tradeStatus;

    /** 交易金额 */
    @Excel(name = "交易金额[币种]/积分")
    private String tradeAmount;

    /** 币种 */
    @Excel(name = "币种")
    private String currency;

    /** 交易时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "交易时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date tradeTime;

    /** 可用金额 */
    @Excel(name = "可用金额/积分")
    private String availableAmount;

    /** 冻结金额 */
    @Excel(name = "冻结金额/积分")
    private String frozenAmount;

    /** 账户金额 */
    @Excel(name = "账户金额/积分")
    private String accountAmount;

    /** 待入账 */
    @Excel(name = "待入账")
    private String pendingAmount;

    /** 押金 */
    @Excel(name = "押金")
    private String deposit;

    /** 交易单号 */
    @Excel(name = "交易单号")
    private String orderCode;

    /** 交易子单号/其他 */
    @Excel(name = "交易子单号/其他")
    private String subOrderCode;

    /** 交易子单号类型 */
    private String subOrderType;

    /** 备注 */
    @Excel(name = "备注")
    private String remark;

    /** 订单号 */
    @Excel(name = "订单号")
    private String orderNo;

    /** 店铺 */
    @Excel(name = "店铺")
    private String shop;

    /** 流水状态：异常=1 正常=2 未知=3 未更新=0 准备rpa=4  */
    private Long flowStatus;

    /** 操作创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date operationCreateTime;

    /** 操作更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date operationUpdateTime;

    /** 执行日志 */
    private String executionLog;

    /** 执行日志分类标签 */
    private String logCategories;

    /** 执行日志关键词搜索（查询参数，不映射数据库） */
    private String executionLogKeyword;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setTradeSerial(String tradeSerial) 
    {
        this.tradeSerial = tradeSerial;
    }

    public String getTradeSerial() 
    {
        return tradeSerial;
    }

    public void setPayAccount(String payAccount) 
    {
        this.payAccount = payAccount;
    }

    public String getPayAccount() 
    {
        return payAccount;
    }

    public void setIncomeType(String incomeType) 
    {
        this.incomeType = incomeType;
    }

    public String getIncomeType() 
    {
        return incomeType;
    }

    public void setTradeType(String tradeType) 
    {
        this.tradeType = tradeType;
    }

    public String getTradeType() 
    {
        return tradeType;
    }

    public void setTradeStatus(String tradeStatus) 
    {
        this.tradeStatus = tradeStatus;
    }

    public String getTradeStatus() 
    {
        return tradeStatus;
    }

    public void setTradeAmount(String tradeAmount) 
    {
        this.tradeAmount = tradeAmount;
    }

    public String getTradeAmount() 
    {
        return tradeAmount;
    }

    public void setCurrency(String currency) 
    {
        this.currency = currency;
    }

    public String getCurrency() 
    {
        return currency;
    }

    public void setTradeTime(Date tradeTime) 
    {
        this.tradeTime = tradeTime;
    }

    public Date getTradeTime() 
    {
        return tradeTime;
    }

    public void setAvailableAmount(String availableAmount) 
    {
        this.availableAmount = availableAmount;
    }

    public String getAvailableAmount() 
    {
        return availableAmount;
    }

    public void setFrozenAmount(String frozenAmount) 
    {
        this.frozenAmount = frozenAmount;
    }

    public String getFrozenAmount() 
    {
        return frozenAmount;
    }

    public void setAccountAmount(String accountAmount) 
    {
        this.accountAmount = accountAmount;
    }

    public String getAccountAmount() 
    {
        return accountAmount;
    }

    public void setPendingAmount(String pendingAmount) 
    {
        this.pendingAmount = pendingAmount;
    }

    public String getPendingAmount() 
    {
        return pendingAmount;
    }

    public void setDeposit(String deposit) 
    {
        this.deposit = deposit;
    }

    public String getDeposit() 
    {
        return deposit;
    }

    public void setOrderCode(String orderCode) 
    {
        this.orderCode = orderCode;
    }

    public String getOrderCode() 
    {
        return orderCode;
    }

    public void setSubOrderCode(String subOrderCode) 
    {
        this.subOrderCode = subOrderCode;
    }

    public String getSubOrderCode() 
    {
        return subOrderCode;
    }

    public void setSubOrderType(String subOrderType) 
    {
        this.subOrderType = subOrderType;
    }

    public String getSubOrderType() 
    {
        return subOrderType;
    }

    public void setRemark(String remark) 
    {
        this.remark = remark;
    }

    public String getRemark() 
    {
        return remark;
    }

    public void setOrderNo(String orderNo) 
    {
        this.orderNo = orderNo;
    }

    public String getOrderNo() 
    {
        return orderNo;
    }

    public void setShop(String shop) 
    {
        this.shop = shop;
    }

    public String getShop() 
    {
        return shop;
    }

    public void setFlowStatus(Long flowStatus) 
    {
        this.flowStatus = flowStatus;
    }

    public Long getFlowStatus() 
    {
        return flowStatus;
    }

    public void setOperationCreateTime(Date operationCreateTime) 
    {
        this.operationCreateTime = operationCreateTime;
    }

    public Date getOperationCreateTime() 
    {
        return operationCreateTime;
    }

    public void setOperationUpdateTime(Date operationUpdateTime) 
    {
        this.operationUpdateTime = operationUpdateTime;
    }

    public Date getOperationUpdateTime() 
    {
        return operationUpdateTime;
    }

    public void setExecutionLog(String executionLog) 
    {
        this.executionLog = executionLog;
    }

    public String getExecutionLog() 
    {
        return executionLog;
    }

    public String getLogCategories() 
    {
        return logCategories;
    }

    public void setLogCategories(String logCategories) 
    {
        this.logCategories = logCategories;
    }

    public String getExecutionLogKeyword() 
    {
        return executionLogKeyword;
    }

    public void setExecutionLogKeyword(String executionLogKeyword) 
    {
        this.executionLogKeyword = executionLogKeyword;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("tradeSerial", getTradeSerial())
            .append("payAccount", getPayAccount())
            .append("incomeType", getIncomeType())
            .append("tradeType", getTradeType())
            .append("tradeStatus", getTradeStatus())
            .append("tradeAmount", getTradeAmount())
            .append("currency", getCurrency())
            .append("tradeTime", getTradeTime())
            .append("availableAmount", getAvailableAmount())
            .append("frozenAmount", getFrozenAmount())
            .append("accountAmount", getAccountAmount())
            .append("pendingAmount", getPendingAmount())
            .append("deposit", getDeposit())
            .append("orderCode", getOrderCode())
            .append("subOrderCode", getSubOrderCode())
            .append("subOrderType", getSubOrderType())
            .append("remark", getRemark())
            .append("orderNo", getOrderNo())
            .append("shop", getShop())
            .append("flowStatus", getFlowStatus())
            .append("operationCreateTime", getOperationCreateTime())
            .append("operationUpdateTime", getOperationUpdateTime())
            .append("executionLog", getExecutionLog())
            .append("logCategories", getLogCategories())
            .append("executionLogKeyword", getExecutionLogKeyword())
            .toString();
    }
}
