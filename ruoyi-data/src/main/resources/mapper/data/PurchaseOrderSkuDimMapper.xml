<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.data.mapper.PurchaseOrderSkuDimMapper">
    
    <resultMap type="PurchaseOrderSkuDim" id="PurchaseOrderSkuDimResult">
        <result property="id"    column="id"    />
        <result property="purchaseOrderNo"    column="purchase_order_no"    />
        <result property="creationTime"    column="creation_time"    />
        <result property="auditTime"    column="audit_time"    />
        <result property="paymentTime"    column="payment_time"    />
        <result property="completionTime"    column="completion_time"    />
        <result property="orderStatus"    column="order_status"    />
        <result property="creationType"    column="creation_type"    />
        <result property="sourceOrderNo"    column="source_order_no"    />
        <result property="systemSku"    column="system_sku"    />
        <result property="customerSku"    column="customer_sku"    />
        <result property="originalPrice"    column="original_price"    />
        <result property="productDiscount"    column="product_discount"    />
        <result property="discountedPrice"    column="discounted_price"    />
        <result property="receivedQuantity"    column="received_quantity"    />
        <result property="normalPurchase"    column="normal_purchase"    />
        <result property="merchantRedundancy"    column="merchant_redundancy"    />
        <result property="cancelledQuantity"    column="cancelled_quantity"    />
        <result property="orderQuantity"    column="order_quantity"    />
        <result property="unreceivedQuantity"    column="unreceived_quantity"    />
        <result property="purchaser"    column="purchaser"    />
        <result property="warehouseCode"    column="warehouse_code"    />
        <result property="warehouseName"    column="warehouse_name"    />
        <result property="cancelReason"    column="cancel_reason"    />
        <result property="inTransitQuantity"    column="in_transit_quantity"    />
        <result property="voidReason"    column="void_reason"    />
        <result property="voidQuantity"    column="void_quantity"    />
        <result property="remark"    column="remark"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectPurchaseOrderSkuDimVo">
        select id, purchase_order_no, creation_time, audit_time, payment_time, completion_time, order_status, creation_type, source_order_no, system_sku, customer_sku, original_price, product_discount, discounted_price, received_quantity, normal_purchase, merchant_redundancy, cancelled_quantity, order_quantity, unreceived_quantity, purchaser, warehouse_code, warehouse_name, cancel_reason, in_transit_quantity, void_reason, void_quantity, remark, create_by, create_time, update_by, update_time from purchase_order_sku_dim
    </sql>

    <select id="selectPurchaseOrderSkuDimList" parameterType="PurchaseOrderSkuDim" resultMap="PurchaseOrderSkuDimResult">
        <include refid="selectPurchaseOrderSkuDimVo"/>
        <include refid="selectPurchaseOrderSkuDimWhere"/>
    </select>

    <!-- 抽取WHERE条件，用于list查询和count查询复用 -->
    <sql id="selectPurchaseOrderSkuDimWhere">
        <where>
            <if test="purchaseOrderNo != null  and purchaseOrderNo != ''"> and purchase_order_no = #{purchaseOrderNo}</if>
            <if test="params.beginCreationTime != null and params.beginCreationTime != '' and params.endCreationTime != null and params.endCreationTime != ''"> and creation_time between #{params.beginCreationTime} and #{params.endCreationTime}</if>
            <if test="params.beginAuditTime != null and params.beginAuditTime != '' and params.endAuditTime != null and params.endAuditTime != ''"> and audit_time between #{params.beginAuditTime} and #{params.endAuditTime}</if>
            <if test="params.beginPaymentTime != null and params.beginPaymentTime != '' and params.endPaymentTime != null and params.endPaymentTime != ''"> and payment_time between #{params.beginPaymentTime} and #{params.endPaymentTime}</if>
            <if test="params.beginCompletionTime != null and params.beginCompletionTime != '' and params.endCompletionTime != null and params.endCompletionTime != ''"> and completion_time between #{params.beginCompletionTime} and #{params.endCompletionTime}</if>
            <if test="orderStatus != null  and orderStatus != ''"> and order_status = #{orderStatus}</if>
            <if test="creationType != null  and creationType != ''"> and creation_type = #{creationType}</if>
            <if test="sourceOrderNo != null  and sourceOrderNo != ''"> and source_order_no = #{sourceOrderNo}</if>
            <if test="systemSku != null  and systemSku != ''"> and system_sku = #{systemSku}</if>
            <if test="customerSku != null  and customerSku != ''"> and customer_sku = #{customerSku}</if>
            <if test="purchaser != null  and purchaser != ''"> and purchaser = #{purchaser}</if>
            <if test="warehouseCode != null  and warehouseCode != ''"> and warehouse_code = #{warehouseCode}</if>
            <if test="warehouseName != null  and warehouseName != ''"> and warehouse_name like concat('%', #{warehouseName}, '%')</if>
        </where>
    </sql>
    
    <select id="selectPurchaseOrderSkuDimById" parameterType="Long" resultMap="PurchaseOrderSkuDimResult">
        <include refid="selectPurchaseOrderSkuDimVo"/>
        where id = #{id}
    </select>

    <insert id="insertPurchaseOrderSkuDim" parameterType="PurchaseOrderSkuDim" useGeneratedKeys="true" keyProperty="id">
        insert into purchase_order_sku_dim
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="purchaseOrderNo != null">purchase_order_no,</if>
            <if test="creationTime != null">creation_time,</if>
            <if test="auditTime != null">audit_time,</if>
            <if test="paymentTime != null">payment_time,</if>
            <if test="completionTime != null">completion_time,</if>
            <if test="orderStatus != null">order_status,</if>
            <if test="creationType != null">creation_type,</if>
            <if test="sourceOrderNo != null">source_order_no,</if>
            <if test="systemSku != null">system_sku,</if>
            <if test="customerSku != null">customer_sku,</if>
            <if test="originalPrice != null">original_price,</if>
            <if test="productDiscount != null">product_discount,</if>
            <if test="discountedPrice != null">discounted_price,</if>
            <if test="receivedQuantity != null">received_quantity,</if>
            <if test="normalPurchase != null">normal_purchase,</if>
            <if test="merchantRedundancy != null">merchant_redundancy,</if>
            <if test="cancelledQuantity != null">cancelled_quantity,</if>
            <if test="orderQuantity != null">order_quantity,</if>
            <if test="unreceivedQuantity != null">unreceived_quantity,</if>
            <if test="purchaser != null">purchaser,</if>
            <if test="warehouseCode != null">warehouse_code,</if>
            <if test="warehouseName != null">warehouse_name,</if>
            <if test="cancelReason != null">cancel_reason,</if>
            <if test="inTransitQuantity != null">in_transit_quantity,</if>
            <if test="voidReason != null">void_reason,</if>
            <if test="voidQuantity != null">void_quantity,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="purchaseOrderNo != null">#{purchaseOrderNo},</if>
            <if test="creationTime != null">#{creationTime},</if>
            <if test="auditTime != null">#{auditTime},</if>
            <if test="paymentTime != null">#{paymentTime},</if>
            <if test="completionTime != null">#{completionTime},</if>
            <if test="orderStatus != null">#{orderStatus},</if>
            <if test="creationType != null">#{creationType},</if>
            <if test="sourceOrderNo != null">#{sourceOrderNo},</if>
            <if test="systemSku != null">#{systemSku},</if>
            <if test="customerSku != null">#{customerSku},</if>
            <if test="originalPrice != null">#{originalPrice},</if>
            <if test="productDiscount != null">#{productDiscount},</if>
            <if test="discountedPrice != null">#{discountedPrice},</if>
            <if test="receivedQuantity != null">#{receivedQuantity},</if>
            <if test="normalPurchase != null">#{normalPurchase},</if>
            <if test="merchantRedundancy != null">#{merchantRedundancy},</if>
            <if test="cancelledQuantity != null">#{cancelledQuantity},</if>
            <if test="orderQuantity != null">#{orderQuantity},</if>
            <if test="unreceivedQuantity != null">#{unreceivedQuantity},</if>
            <if test="purchaser != null">#{purchaser},</if>
            <if test="warehouseCode != null">#{warehouseCode},</if>
            <if test="warehouseName != null">#{warehouseName},</if>
            <if test="cancelReason != null">#{cancelReason},</if>
            <if test="inTransitQuantity != null">#{inTransitQuantity},</if>
            <if test="voidReason != null">#{voidReason},</if>
            <if test="voidQuantity != null">#{voidQuantity},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updatePurchaseOrderSkuDim" parameterType="PurchaseOrderSkuDim">
        update purchase_order_sku_dim
        <trim prefix="SET" suffixOverrides=",">
            <if test="purchaseOrderNo != null">purchase_order_no = #{purchaseOrderNo},</if>
            <if test="creationTime != null">creation_time = #{creationTime},</if>
            <if test="auditTime != null">audit_time = #{auditTime},</if>
            <if test="paymentTime != null">payment_time = #{paymentTime},</if>
            <if test="completionTime != null">completion_time = #{completionTime},</if>
            <if test="orderStatus != null">order_status = #{orderStatus},</if>
            <if test="creationType != null">creation_type = #{creationType},</if>
            <if test="sourceOrderNo != null">source_order_no = #{sourceOrderNo},</if>
            <if test="systemSku != null">system_sku = #{systemSku},</if>
            <if test="customerSku != null">customer_sku = #{customerSku},</if>
            <if test="originalPrice != null">original_price = #{originalPrice},</if>
            <if test="productDiscount != null">product_discount = #{productDiscount},</if>
            <if test="discountedPrice != null">discounted_price = #{discountedPrice},</if>
            <if test="receivedQuantity != null">received_quantity = #{receivedQuantity},</if>
            <if test="normalPurchase != null">normal_purchase = #{normalPurchase},</if>
            <if test="merchantRedundancy != null">merchant_redundancy = #{merchantRedundancy},</if>
            <if test="cancelledQuantity != null">cancelled_quantity = #{cancelledQuantity},</if>
            <if test="orderQuantity != null">order_quantity = #{orderQuantity},</if>
            <if test="unreceivedQuantity != null">unreceived_quantity = #{unreceivedQuantity},</if>
            <if test="purchaser != null">purchaser = #{purchaser},</if>
            <if test="warehouseCode != null">warehouse_code = #{warehouseCode},</if>
            <if test="warehouseName != null">warehouse_name = #{warehouseName},</if>
            <if test="cancelReason != null">cancel_reason = #{cancelReason},</if>
            <if test="inTransitQuantity != null">in_transit_quantity = #{inTransitQuantity},</if>
            <if test="voidReason != null">void_reason = #{voidReason},</if>
            <if test="voidQuantity != null">void_quantity = #{voidQuantity},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePurchaseOrderSkuDimById" parameterType="Long">
        delete from purchase_order_sku_dim where id = #{id}
    </delete>

    <delete id="deletePurchaseOrderSkuDimByIds" parameterType="String">
        delete from purchase_order_sku_dim where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 分页查询采购单-sku维度列表（用于流式导出全量数据） -->
    <select id="selectPurchaseOrderSkuDimListByPage" parameterType="PurchaseOrderSkuDim" resultMap="PurchaseOrderSkuDimResult">
        <include refid="selectPurchaseOrderSkuDimVo"/>
        <include refid="selectPurchaseOrderSkuDimWhere"/>
        order by id asc
    </select>

    <!-- 批量插入采购单-sku维度 -->
    <insert id="batchInsertPurchaseOrderSkuDim" parameterType="java.util.List">
        insert into purchase_order_sku_dim
        (purchase_order_no, creation_time, audit_time, payment_time, completion_time, order_status,
         creation_type, source_order_no, system_sku, customer_sku, original_price, product_discount,
         discounted_price, received_quantity, normal_purchase, merchant_redundancy, cancelled_quantity,
         order_quantity, unreceived_quantity, purchaser, warehouse_code, warehouse_name, cancel_reason,
         in_transit_quantity, void_reason, void_quantity, remark, create_by, create_time, update_by, update_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.purchaseOrderNo}, #{item.creationTime}, #{item.auditTime}, #{item.paymentTime},
             #{item.completionTime}, #{item.orderStatus}, #{item.creationType}, #{item.sourceOrderNo},
             #{item.systemSku}, #{item.customerSku}, #{item.originalPrice}, #{item.productDiscount},
             #{item.discountedPrice}, #{item.receivedQuantity}, #{item.normalPurchase}, #{item.merchantRedundancy},
             #{item.cancelledQuantity}, #{item.orderQuantity}, #{item.unreceivedQuantity}, #{item.purchaser},
             #{item.warehouseCode}, #{item.warehouseName}, #{item.cancelReason}, #{item.inTransitQuantity},
             #{item.voidReason}, #{item.voidQuantity}, #{item.remark}, #{item.createBy}, #{item.createTime},
             #{item.updateBy}, #{item.updateTime})
        </foreach>
    </insert>

    <!-- 根据采购单号查询采购单-sku维度列表 -->
    <select id="selectByPurchaseOrderNo" parameterType="String" resultMap="PurchaseOrderSkuDimResult">
        <include refid="selectPurchaseOrderSkuDimVo"/>
        where purchase_order_no = #{purchaseOrderNo}
        order by id asc
    </select>

    <!-- 根据采购单号和系统SKU查询采购单-sku维度 -->
    <select id="selectByPurchaseOrderNoAndSystemSku" resultMap="PurchaseOrderSkuDimResult">
        <include refid="selectPurchaseOrderSkuDimVo"/>
        where purchase_order_no = #{purchaseOrderNo} and system_sku = #{systemSku}
        limit 1
    </select>

    <!-- 批量更新或插入采购单-sku维度（MySQL ON DUPLICATE KEY UPDATE） -->
    <insert id="batchUpsertPurchaseOrderSkuDim" parameterType="java.util.List">
        insert into purchase_order_sku_dim
        (purchase_order_no, creation_time, audit_time, payment_time, completion_time, order_status,
         creation_type, source_order_no, system_sku, customer_sku, original_price, product_discount,
         discounted_price, received_quantity, normal_purchase, merchant_redundancy, cancelled_quantity,
         order_quantity, unreceived_quantity, purchaser, warehouse_code, warehouse_name, cancel_reason,
         in_transit_quantity, void_reason, void_quantity, remark, create_by, create_time, update_by, update_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.purchaseOrderNo}, #{item.creationTime}, #{item.auditTime}, #{item.paymentTime}, #{item.completionTime}, #{item.orderStatus},
             #{item.creationType}, #{item.sourceOrderNo}, #{item.systemSku}, #{item.customerSku}, #{item.originalPrice}, #{item.productDiscount},
             #{item.discountedPrice}, #{item.receivedQuantity}, #{item.normalPurchase}, #{item.merchantRedundancy}, #{item.cancelledQuantity},
             #{item.orderQuantity}, #{item.unreceivedQuantity}, #{item.purchaser}, #{item.warehouseCode}, #{item.warehouseName}, #{item.cancelReason},
             #{item.inTransitQuantity}, #{item.voidReason}, #{item.voidQuantity}, #{item.remark}, #{item.createBy}, #{item.createTime}, #{item.updateBy}, #{item.updateTime})
        </foreach>
        ON DUPLICATE KEY UPDATE
            creation_time = VALUES(creation_time),
            audit_time = VALUES(audit_time),
            payment_time = VALUES(payment_time),
            completion_time = VALUES(completion_time),
            order_status = VALUES(order_status),
            creation_type = VALUES(creation_type),
            source_order_no = VALUES(source_order_no),
            customer_sku = VALUES(customer_sku),
            original_price = VALUES(original_price),
            product_discount = VALUES(product_discount),
            discounted_price = VALUES(discounted_price),
            received_quantity = VALUES(received_quantity),
            normal_purchase = VALUES(normal_purchase),
            merchant_redundancy = VALUES(merchant_redundancy),
            cancelled_quantity = VALUES(cancelled_quantity),
            order_quantity = VALUES(order_quantity),
            unreceived_quantity = VALUES(unreceived_quantity),
            purchaser = VALUES(purchaser),
            warehouse_code = VALUES(warehouse_code),
            warehouse_name = VALUES(warehouse_name),
            cancel_reason = VALUES(cancel_reason),
            in_transit_quantity = VALUES(in_transit_quantity),
            void_reason = VALUES(void_reason),
            void_quantity = VALUES(void_quantity),
            remark = VALUES(remark),
            update_by = VALUES(update_by),
            update_time = VALUES(update_time)
    </insert>

    <!-- 批量更新采购单-sku维度 -->
    <update id="batchUpdatePurchaseOrderSkuDim" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            update purchase_order_sku_dim
            <set>
                <if test="item.creationTime != null">creation_time = #{item.creationTime},</if>
                <if test="item.auditTime != null">audit_time = #{item.auditTime},</if>
                <if test="item.paymentTime != null">payment_time = #{item.paymentTime},</if>
                <if test="item.completionTime != null">completion_time = #{item.completionTime},</if>
                <if test="item.orderStatus != null">order_status = #{item.orderStatus},</if>
                <if test="item.creationType != null">creation_type = #{item.creationType},</if>
                <if test="item.sourceOrderNo != null">source_order_no = #{item.sourceOrderNo},</if>
                <if test="item.customerSku != null">customer_sku = #{item.customerSku},</if>
                <if test="item.originalPrice != null">original_price = #{item.originalPrice},</if>
                <if test="item.productDiscount != null">product_discount = #{item.productDiscount},</if>
                <if test="item.discountedPrice != null">discounted_price = #{item.discountedPrice},</if>
                <if test="item.receivedQuantity != null">received_quantity = #{item.receivedQuantity},</if>
                <if test="item.normalPurchase != null">normal_purchase = #{item.normalPurchase},</if>
                <if test="item.merchantRedundancy != null">merchant_redundancy = #{item.merchantRedundancy},</if>
                <if test="item.cancelledQuantity != null">cancelled_quantity = #{item.cancelledQuantity},</if>
                <if test="item.orderQuantity != null">order_quantity = #{item.orderQuantity},</if>
                <if test="item.unreceivedQuantity != null">unreceived_quantity = #{item.unreceivedQuantity},</if>
                <if test="item.purchaser != null">purchaser = #{item.purchaser},</if>
                <if test="item.warehouseCode != null">warehouse_code = #{item.warehouseCode},</if>
                <if test="item.warehouseName != null">warehouse_name = #{item.warehouseName},</if>
                <if test="item.cancelReason != null">cancel_reason = #{item.cancelReason},</if>
                <if test="item.inTransitQuantity != null">in_transit_quantity = #{item.inTransitQuantity},</if>
                <if test="item.voidReason != null">void_reason = #{item.voidReason},</if>
                <if test="item.voidQuantity != null">void_quantity = #{item.voidQuantity},</if>
                <if test="item.remark != null">remark = #{item.remark},</if>
                <if test="item.updateBy != null">update_by = #{item.updateBy},</if>
                <if test="item.updateTime != null">update_time = #{item.updateTime},</if>
            </set>
            where purchase_order_no = #{item.purchaseOrderNo} and system_sku = #{item.systemSku}
        </foreach>
    </update>
</mapper>