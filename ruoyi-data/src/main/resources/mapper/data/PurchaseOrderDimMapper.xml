<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.data.mapper.PurchaseOrderDimMapper">
    
    <resultMap type="PurchaseOrderDim" id="PurchaseOrderDimResult">
        <result property="id"    column="id"    />
        <result property="purchaseOrderNo"    column="purchase_order_no"    />
        <result property="creationTime"    column="creation_time"    />
        <result property="auditTime"    column="audit_time"    />
        <result property="paymentTime"    column="payment_time"    />
        <result property="completionTime"    column="completion_time"    />
        <result property="orderStatus"    column="order_status"    />
        <result property="creationType"    column="creation_type"    />
        <result property="sourceOrderNo"    column="source_order_no"    />
        <result property="productCategory"    column="product_category"    />
        <result property="orderQuantity"    column="order_quantity"    />
        <result property="purchaseTotal"    column="purchase_total"    />
        <result property="unreceivedQuantity"    column="unreceived_quantity"    />
        <result property="totalReceived"    column="total_received"    />
        <result property="cancelledTotal"    column="cancelled_total"    />
        <result property="revokedTotal"    column="revoked_total"    />
        <result property="refundAmount"    column="refund_amount"    />
        <result property="purchaser"    column="purchaser"    />
        <result property="warehouseCode"    column="warehouse_code"    />
        <result property="warehouseName"    column="warehouse_name"    />
        <result property="remark"    column="remark"    />
        <result property="platformOrderNo"    column="platform_order_no"    />
        <result property="orderNo"    column="order_no"    />
        <result property="storeAccount"    column="store_account"    />
    </resultMap>

    <sql id="selectPurchaseOrderDimVo">
        select id, purchase_order_no, creation_time, audit_time, payment_time, completion_time, order_status, creation_type, source_order_no, product_category, order_quantity, purchase_total, unreceived_quantity, total_received, cancelled_total, revoked_total, refund_amount, purchaser, warehouse_code, warehouse_name, remark, platform_order_no, order_no, store_account from purchase_order_dim
    </sql>

    <select id="selectPurchaseOrderDimList" parameterType="PurchaseOrderDim" resultMap="PurchaseOrderDimResult">
        <include refid="selectPurchaseOrderDimVo"/>
        <include refid="selectPurchaseOrderDimWhere"/>
    </select>
    
    <select id="selectPurchaseOrderDimById" parameterType="Long" resultMap="PurchaseOrderDimResult">
        <include refid="selectPurchaseOrderDimVo"/>
        where id = #{id}
    </select>

    <!-- 统计采购订单维度数量 -->
    <select id="countPurchaseOrderDim" parameterType="PurchaseOrderDim" resultType="long">
        select count(*) from purchase_order_dim
        <include refid="selectPurchaseOrderDimWhere"/>
    </select>

    <!-- 分页查询采购订单维度列表（用于流式导出全量数据） -->
    <select id="selectPurchaseOrderDimListByPage" parameterType="PurchaseOrderDim" resultMap="PurchaseOrderDimResult">
        <include refid="selectPurchaseOrderDimVo"/>
        <include refid="selectPurchaseOrderDimWhere"/>
        order by id asc
    </select>

    <!-- 抽取WHERE条件，用于list查询和count查询复用 -->
    <sql id="selectPurchaseOrderDimWhere">
        <where>  
            <if test="purchaseOrderNo != null  and purchaseOrderNo != ''"> and purchase_order_no = #{purchaseOrderNo}</if>
            <if test="params.beginCreationTime != null and params.beginCreationTime != '' and params.endCreationTime != null and params.endCreationTime != ''"> and creation_time between #{params.beginCreationTime} and #{params.endCreationTime}</if>
            <if test="params.beginAuditTime != null and params.beginAuditTime != '' and params.endAuditTime != null and params.endAuditTime != ''"> and audit_time between #{params.beginAuditTime} and #{params.endAuditTime}</if>
            <if test="params.beginPaymentTime != null and params.beginPaymentTime != '' and params.endPaymentTime != null and params.endPaymentTime != ''"> and payment_time between #{params.beginPaymentTime} and #{params.endPaymentTime}</if>
            <if test="params.beginCompletionTime != null and params.beginCompletionTime != '' and params.endCompletionTime != null and params.endCompletionTime != ''"> and completion_time between #{params.beginCompletionTime} and #{params.endCompletionTime}</if>
            <if test="orderStatus != null  and orderStatus != ''"> and order_status = #{orderStatus}</if>
            <if test="creationType != null  and creationType != ''"> and creation_type = #{creationType}</if>
            <if test="sourceOrderNo != null  and sourceOrderNo != ''"> and source_order_no = #{sourceOrderNo}</if>
            <if test="productCategory != null  and productCategory != ''"> and product_category = #{productCategory}</if>
            <if test="warehouseCode != null  and warehouseCode != ''"> and warehouse_code = #{warehouseCode}</if>
            <if test="warehouseName != null  and warehouseName != ''"> and warehouse_name like concat('%', #{warehouseName}, '%')</if>
            <if test="platformOrderNo != null  and platformOrderNo != ''"> and platform_order_no = #{platformOrderNo}</if>
            <if test="orderNo != null  and orderNo != ''"> and order_no = #{orderNo}</if>
            <if test="storeAccount != null  and storeAccount != ''"> and store_account = #{storeAccount}</if>
        </where>
    </sql>

    <insert id="insertPurchaseOrderDim" parameterType="PurchaseOrderDim" useGeneratedKeys="true" keyProperty="id">
        insert into purchase_order_dim
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="purchaseOrderNo != null">purchase_order_no,</if>
            <if test="creationTime != null">creation_time,</if>
            <if test="auditTime != null">audit_time,</if>
            <if test="paymentTime != null">payment_time,</if>
            <if test="completionTime != null">completion_time,</if>
            <if test="orderStatus != null">order_status,</if>
            <if test="creationType != null">creation_type,</if>
            <if test="sourceOrderNo != null">source_order_no,</if>
            <if test="productCategory != null">product_category,</if>
            <if test="orderQuantity != null">order_quantity,</if>
            <if test="purchaseTotal != null">purchase_total,</if>
            <if test="unreceivedQuantity != null">unreceived_quantity,</if>
            <if test="totalReceived != null">total_received,</if>
            <if test="cancelledTotal != null">cancelled_total,</if>
            <if test="revokedTotal != null">revoked_total,</if>
            <if test="refundAmount != null">refund_amount,</if>
            <if test="purchaser != null">purchaser,</if>
            <if test="warehouseCode != null">warehouse_code,</if>
            <if test="warehouseName != null">warehouse_name,</if>
            <if test="remark != null">remark,</if>
            <if test="platformOrderNo != null">platform_order_no,</if>
            <if test="orderNo != null">order_no,</if>
            <if test="storeAccount != null">store_account,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="purchaseOrderNo != null">#{purchaseOrderNo},</if>
            <if test="creationTime != null">#{creationTime},</if>
            <if test="auditTime != null">#{auditTime},</if>
            <if test="paymentTime != null">#{paymentTime},</if>
            <if test="completionTime != null">#{completionTime},</if>
            <if test="orderStatus != null">#{orderStatus},</if>
            <if test="creationType != null">#{creationType},</if>
            <if test="sourceOrderNo != null">#{sourceOrderNo},</if>
            <if test="productCategory != null">#{productCategory},</if>
            <if test="orderQuantity != null">#{orderQuantity},</if>
            <if test="purchaseTotal != null">#{purchaseTotal},</if>
            <if test="unreceivedQuantity != null">#{unreceivedQuantity},</if>
            <if test="totalReceived != null">#{totalReceived},</if>
            <if test="cancelledTotal != null">#{cancelledTotal},</if>
            <if test="revokedTotal != null">#{revokedTotal},</if>
            <if test="refundAmount != null">#{refundAmount},</if>
            <if test="purchaser != null">#{purchaser},</if>
            <if test="warehouseCode != null">#{warehouseCode},</if>
            <if test="warehouseName != null">#{warehouseName},</if>
            <if test="remark != null">#{remark},</if>
            <if test="platformOrderNo != null">#{platformOrderNo},</if>
            <if test="orderNo != null">#{orderNo},</if>
            <if test="storeAccount != null">#{storeAccount},</if>
         </trim>
    </insert>

    <update id="updatePurchaseOrderDim" parameterType="PurchaseOrderDim">
        update purchase_order_dim
        <trim prefix="SET" suffixOverrides=",">
            <if test="purchaseOrderNo != null">purchase_order_no = #{purchaseOrderNo},</if>
            <if test="creationTime != null">creation_time = #{creationTime},</if>
            <if test="auditTime != null">audit_time = #{auditTime},</if>
            <if test="paymentTime != null">payment_time = #{paymentTime},</if>
            <if test="completionTime != null">completion_time = #{completionTime},</if>
            <if test="orderStatus != null">order_status = #{orderStatus},</if>
            <if test="creationType != null">creation_type = #{creationType},</if>
            <if test="sourceOrderNo != null">source_order_no = #{sourceOrderNo},</if>
            <if test="productCategory != null">product_category = #{productCategory},</if>
            <if test="orderQuantity != null">order_quantity = #{orderQuantity},</if>
            <if test="purchaseTotal != null">purchase_total = #{purchaseTotal},</if>
            <if test="unreceivedQuantity != null">unreceived_quantity = #{unreceivedQuantity},</if>
            <if test="totalReceived != null">total_received = #{totalReceived},</if>
            <if test="cancelledTotal != null">cancelled_total = #{cancelledTotal},</if>
            <if test="revokedTotal != null">revoked_total = #{revokedTotal},</if>
            <if test="refundAmount != null">refund_amount = #{refundAmount},</if>
            <if test="purchaser != null">purchaser = #{purchaser},</if>
            <if test="warehouseCode != null">warehouse_code = #{warehouseCode},</if>
            <if test="warehouseName != null">warehouse_name = #{warehouseName},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="platformOrderNo != null">platform_order_no = #{platformOrderNo},</if>
            <if test="orderNo != null">order_no = #{orderNo},</if>
            <if test="storeAccount != null">store_account = #{storeAccount},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePurchaseOrderDimById" parameterType="Long">
        delete from purchase_order_dim where id = #{id}
    </delete>

    <delete id="deletePurchaseOrderDimByIds" parameterType="String">
        delete from purchase_order_dim where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 批量插入采购订单维度 -->
    <insert id="batchInsertPurchaseOrderDim" parameterType="java.util.List">
        insert into purchase_order_dim
        (purchase_order_no, creation_time, audit_time, payment_time, completion_time, order_status, 
         creation_type, source_order_no, product_category, order_quantity, purchase_total, 
         unreceived_quantity, total_received, cancelled_total, revoked_total, refund_amount, 
         purchaser, warehouse_code, warehouse_name, remark, platform_order_no, order_no, store_account)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.purchaseOrderNo}, #{item.creationTime}, #{item.auditTime}, #{item.paymentTime}, 
             #{item.completionTime}, #{item.orderStatus}, #{item.creationType}, #{item.sourceOrderNo}, 
             #{item.productCategory}, #{item.orderQuantity}, #{item.purchaseTotal}, #{item.unreceivedQuantity}, 
             #{item.totalReceived}, #{item.cancelledTotal}, #{item.revokedTotal}, #{item.refundAmount}, 
             #{item.purchaser}, #{item.warehouseCode}, #{item.warehouseName}, #{item.remark}, 
             #{item.platformOrderNo}, #{item.orderNo}, #{item.storeAccount})
        </foreach>
    </insert>

    <!-- 根据采购单号查询采购订单维度 -->
    <select id="selectByPurchaseOrderNo" parameterType="String" resultMap="PurchaseOrderDimResult">
        <include refid="selectPurchaseOrderDimVo"/>
        where purchase_order_no = #{purchaseOrderNo}
    </select>

    <!-- 根据采购单号批量查询采购订单维度 -->
    <select id="selectByPurchaseOrderNos" parameterType="java.util.List" resultMap="PurchaseOrderDimResult">
        <include refid="selectPurchaseOrderDimVo"/>
        where purchase_order_no in
        <foreach collection="list" item="purchaseOrderNo" open="(" separator="," close=")">
            #{purchaseOrderNo}
        </foreach>
    </select>

    <!-- 批量更新采购订单维度 -->
    <update id="batchUpdatePurchaseOrderDim" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            update purchase_order_dim
            <set>
                <if test="item.creationTime != null">creation_time = #{item.creationTime},</if>
                <if test="item.auditTime != null">audit_time = #{item.auditTime},</if>
                <if test="item.paymentTime != null">payment_time = #{item.paymentTime},</if>
                <if test="item.completionTime != null">completion_time = #{item.completionTime},</if>
                <if test="item.orderStatus != null">order_status = #{item.orderStatus},</if>
                <if test="item.creationType != null">creation_type = #{item.creationType},</if>
                <if test="item.sourceOrderNo != null">source_order_no = #{item.sourceOrderNo},</if>
                <if test="item.productCategory != null">product_category = #{item.productCategory},</if>
                <if test="item.orderQuantity != null">order_quantity = #{item.orderQuantity},</if>
                <if test="item.purchaseTotal != null">purchase_total = #{item.purchaseTotal},</if>
                <if test="item.unreceivedQuantity != null">unreceived_quantity = #{item.unreceivedQuantity},</if>
                <if test="item.totalReceived != null">total_received = #{item.totalReceived},</if>
                <if test="item.cancelledTotal != null">cancelled_total = #{item.cancelledTotal},</if>
                <if test="item.revokedTotal != null">revoked_total = #{item.revokedTotal},</if>
                <if test="item.refundAmount != null">refund_amount = #{item.refundAmount},</if>
                <if test="item.purchaser != null">purchaser = #{item.purchaser},</if>
                <if test="item.warehouseCode != null">warehouse_code = #{item.warehouseCode},</if>
                <if test="item.warehouseName != null">warehouse_name = #{item.warehouseName},</if>
                <if test="item.remark != null">remark = #{item.remark},</if>
                <if test="item.platformOrderNo != null">platform_order_no = #{item.platformOrderNo},</if>
                <if test="item.orderNo != null">order_no = #{item.orderNo},</if>
                <if test="item.storeAccount != null">store_account = #{item.storeAccount},</if>
            </set>
            where purchase_order_no = #{item.purchaseOrderNo}
        </foreach>
    </update>
</mapper>