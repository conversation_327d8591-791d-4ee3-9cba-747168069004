-- 为采购单SKU维度表添加唯一索引
-- 索引名称：uk_purchase_order_sku_dim_order_sku
-- 索引字段：purchase_order_no + system_sku

-- 1. 首先检查是否存在重复数据，如果有重复数据需要先清理
SELECT 
    purchase_order_no, 
    system_sku, 
    COUNT(*) as duplicate_count
FROM purchase_order_sku_dim 
WHERE purchase_order_no IS NOT NULL 
  AND system_sku IS NOT NULL
GROUP BY purchase_order_no, system_sku 
HAVING COUNT(*) > 1;

-- 2. 如果存在重复数据，可以使用以下SQL保留最新的记录，删除旧的重复记录
-- DELETE p1 FROM purchase_order_sku_dim p1
-- INNER JOIN purchase_order_sku_dim p2 
-- WHERE p1.purchase_order_no = p2.purchase_order_no 
--   AND p1.system_sku = p2.system_sku
--   AND p1.id < p2.id;

-- 3. 添加唯一索引
ALTER TABLE purchase_order_sku_dim 
ADD UNIQUE INDEX uk_purchase_order_sku_dim_order_sku (purchase_order_no, system_sku);

-- 4. 验证索引是否创建成功
SHOW INDEX FROM purchase_order_sku_dim WHERE Key_name = 'uk_purchase_order_sku_dim_order_sku';
