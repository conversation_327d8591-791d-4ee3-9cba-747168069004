package com.ruoyi.data.template;

import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.data.domain.AccountTradeDetail;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.ByteArrayOutputStream;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.*;

/**
 * 账户交易明细导入模板测试
 * 验证模板中不包含系统自动管理的字段
 * 
 * <AUTHOR>
 * @date 2025-01-28
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class AccountTradeDetailTemplateTest {

    /**
     * 测试导入模板字段
     * 验证不应该出现在模板中的字段确实被排除了
     */
    @Test
    public void testImportTemplateFields() throws Exception {
        // 创建ExcelUtil实例
        ExcelUtil<AccountTradeDetail> util = new ExcelUtil<>(AccountTradeDetail.class);
        
        // 获取所有带@Excel注解的字段
        List<String> excelFields = getExcelAnnotatedFields();
        
        // 验证不应该出现在模板中的字段
        assertFalse("流水状态字段不应该出现在导入模板中", 
                   excelFields.contains("流水状态：异常=1 正常=2 未知=3 未更新=0 准备rpa=4"));
        
        assertFalse("交易子单号类型字段不应该出现在导入模板中", 
                   excelFields.contains("交易子单号类型"));
        
        // 验证应该出现在模板中的关键字段
        assertTrue("交易流水号字段应该出现在导入模板中", 
                  excelFields.contains("交易流水号"));
        
        assertTrue("支付账号字段应该出现在导入模板中", 
                  excelFields.contains("支付账号"));
        
        assertTrue("交易类型字段应该出现在导入模板中", 
                  excelFields.contains("交易类型"));
        
        assertTrue("店铺字段应该出现在导入模板中", 
                  excelFields.contains("店铺"));
        
        // 打印所有Excel字段，便于验证
        System.out.println("=== 导入模板包含的字段 ===");
        for (String fieldName : excelFields) {
            System.out.println("- " + fieldName);
        }
        
        // 验证字段数量合理（应该是18个字段，移除了流水状态和交易子单号类型）
        assertEquals("导入模板字段数量应该是18个", 18, excelFields.size());
    }
    
    /**
     * 测试生成导入模板文件
     * 确保模板可以正常生成
     */
    @Test
    public void testGenerateImportTemplate() throws Exception {
        ExcelUtil<AccountTradeDetail> util = new ExcelUtil<>(AccountTradeDetail.class);
        
        // 生成模板到内存
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        
        try {
            // 这里模拟生成模板的过程
            // 实际的importTemplateExcel方法需要HttpServletResponse，这里只是验证不会抛异常
            assertNotNull("ExcelUtil实例应该正常创建", util);
            
            System.out.println("✅ 导入模板生成测试通过");
            
        } catch (Exception e) {
            fail("生成导入模板时不应该抛出异常: " + e.getMessage());
        }
    }
    
    /**
     * 获取所有带@Excel注解的字段名称
     */
    private List<String> getExcelAnnotatedFields() {
        List<String> fieldNames = new ArrayList<>();
        
        Field[] fields = AccountTradeDetail.class.getDeclaredFields();
        for (Field field : fields) {
            if (field.isAnnotationPresent(com.ruoyi.common.annotation.Excel.class)) {
                com.ruoyi.common.annotation.Excel excelAnnotation = 
                    field.getAnnotation(com.ruoyi.common.annotation.Excel.class);
                fieldNames.add(excelAnnotation.name());
            }
        }
        
        return fieldNames;
    }
    
    /**
     * 测试系统自动管理的字段
     * 验证这些字段确实没有@Excel注解
     */
    @Test
    public void testSystemManagedFields() throws Exception {
        Field[] fields = AccountTradeDetail.class.getDeclaredFields();
        
        for (Field field : fields) {
            String fieldName = field.getName();
            
            // 检查系统自动管理的字段
            if ("flowStatus".equals(fieldName)) {
                assertFalse("flowStatus字段不应该有@Excel注解", 
                           field.isAnnotationPresent(com.ruoyi.common.annotation.Excel.class));
            }
            
            if ("subOrderType".equals(fieldName)) {
                assertFalse("subOrderType字段不应该有@Excel注解", 
                           field.isAnnotationPresent(com.ruoyi.common.annotation.Excel.class));
            }
            
            if ("operationCreateTime".equals(fieldName)) {
                assertFalse("operationCreateTime字段不应该有@Excel注解", 
                           field.isAnnotationPresent(com.ruoyi.common.annotation.Excel.class));
            }
            
            if ("operationUpdateTime".equals(fieldName)) {
                assertFalse("operationUpdateTime字段不应该有@Excel注解", 
                           field.isAnnotationPresent(com.ruoyi.common.annotation.Excel.class));
            }
            
            if ("executionLog".equals(fieldName)) {
                assertFalse("executionLog字段不应该有@Excel注解", 
                           field.isAnnotationPresent(com.ruoyi.common.annotation.Excel.class));
            }
        }
        
        System.out.println("✅ 系统自动管理字段验证通过");
    }
}
