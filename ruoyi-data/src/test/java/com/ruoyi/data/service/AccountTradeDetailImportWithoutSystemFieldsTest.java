package com.ruoyi.data.service;

import com.ruoyi.data.domain.AccountTradeDetail;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static org.junit.Assert.*;

/**
 * 账户交易明细导入功能测试（不包含系统管理字段）
 * 验证移除系统管理字段后导入功能仍然正常工作
 * 
 * <AUTHOR>
 * @date 2025-01-28
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@Transactional
public class AccountTradeDetailImportWithoutSystemFieldsTest {

    @Autowired
    private IAccountTradeDetailService accountTradeDetailService;

    /**
     * 测试导入数据时系统自动设置字段
     */
    @Test
    public void testImportWithSystemAutoFields() throws Exception {
        // 创建模拟导入数据（不包含系统管理字段）
        List<AccountTradeDetail> importList = new ArrayList<>();
        
        // 测试数据1：有店铺信息
        AccountTradeDetail detail1 = new AccountTradeDetail();
        detail1.setTradeSerial("IMPORT_TEST_001_" + System.currentTimeMillis());
        detail1.setPayAccount("<EMAIL>");
        detail1.setIncomeType("收入");
        detail1.setTradeType("订单");
        detail1.setTradeStatus("成功");
        detail1.setTradeAmount("100.00");
        detail1.setCurrency("CNY");
        detail1.setTradeTime(new Date());
        detail1.setAvailableAmount("1000.00");
        detail1.setFrozenAmount("0.00");
        detail1.setAccountAmount("1100.00");
        detail1.setPendingAmount("0.00");
        detail1.setDeposit("0.00");
        detail1.setOrderCode("ORDER001");
        detail1.setSubOrderCode("SUB001");
        detail1.setRemark("测试订单1");
        detail1.setOrderNo("ORDER001");
        detail1.setShop("京东旗舰店"); // 有店铺信息
        // 注意：不设置flowStatus和subOrderType，让系统自动处理
        
        // 测试数据2：无店铺信息
        AccountTradeDetail detail2 = new AccountTradeDetail();
        detail2.setTradeSerial("IMPORT_TEST_002_" + System.currentTimeMillis());
        detail2.setPayAccount("<EMAIL>");
        detail2.setIncomeType("支出");
        detail2.setTradeType("采购单");
        detail2.setTradeStatus("成功");
        detail2.setTradeAmount("150.00");
        detail2.setCurrency("CNY");
        detail2.setTradeTime(new Date());
        detail2.setAvailableAmount("850.00");
        detail2.setFrozenAmount("0.00");
        detail2.setAccountAmount("850.00");
        detail2.setPendingAmount("0.00");
        detail2.setDeposit("0.00");
        detail2.setOrderCode("PURCHASE001");
        detail2.setSubOrderCode("SUB003");
        detail2.setRemark("测试采购1");
        detail2.setOrderNo("PURCHASE001");
        // 不设置店铺信息
        
        importList.add(detail1);
        importList.add(detail2);
        
        // 执行导入
        String result = accountTradeDetailService.importAccountTradeDetail(importList);
        
        // 验证导入结果
        assertNotNull("导入结果不应该为空", result);
        assertTrue("导入应该成功", result.contains("成功"));
        
        // 验证数据1（有店铺信息）
        assertEquals("有店铺信息的记录应该设置为成功状态", Long.valueOf(2L), detail1.getFlowStatus());
        assertEquals("子单号类型应该自动推导", "其他", detail1.getSubOrderType());
        assertNotNull("执行日志应该被设置", detail1.getExecutionLog());
        assertTrue("执行日志应该包含店铺信息", detail1.getExecutionLog().contains("京东旗舰店"));
        assertTrue("执行日志应该包含导入标识", detail1.getExecutionLog().contains("通过Excel表导入更新"));
        
        // 验证数据2（无店铺信息）
        assertEquals("无店铺信息的记录应该设置为未处理状态", Long.valueOf(0L), detail2.getFlowStatus());
        assertEquals("子单号类型应该自动推导", "其他", detail2.getSubOrderType());
        assertNotNull("执行日志应该被设置", detail2.getExecutionLog());
        assertFalse("执行日志不应该包含店铺信息", detail2.getExecutionLog().contains("店铺"));
        
        System.out.println("✅ 导入功能测试通过");
        System.out.println("数据1执行日志: " + detail1.getExecutionLog());
        System.out.println("数据2执行日志: " + detail2.getExecutionLog());
    }
    
    /**
     * 测试子单号类型自动推导功能
     */
    @Test
    public void testSubOrderTypeAutoDeduction() throws Exception {
        List<AccountTradeDetail> testList = new ArrayList<>();
        
        // 测试不同类型的子单号
        String[] subOrderCodes = {
            "12345订单号",      // 应该推导为"订单"
            "SKU_12345",       // 应该推导为"sku"
            "OTHER123",        // 应该推导为"其他"
            "",                // 应该推导为"未知"
            null               // 应该推导为"未知"
        };
        
        String[] expectedTypes = {"订单", "sku", "其他", "未知", "未知"};
        
        for (int i = 0; i < subOrderCodes.length; i++) {
            AccountTradeDetail detail = new AccountTradeDetail();
            detail.setTradeSerial("SUB_TYPE_TEST_" + i + "_" + System.currentTimeMillis());
            detail.setPayAccount("<EMAIL>");
            detail.setIncomeType("收入");
            detail.setTradeType("订单");
            detail.setTradeStatus("成功");
            detail.setTradeAmount("100.00");
            detail.setCurrency("CNY");
            detail.setTradeTime(new Date());
            detail.setOrderCode("ORDER" + i);
            detail.setSubOrderCode(subOrderCodes[i]);
            detail.setOrderNo("ORDER" + i);
            
            testList.add(detail);
        }
        
        // 执行导入
        String result = accountTradeDetailService.importAccountTradeDetail(testList);
        
        // 验证结果
        assertNotNull("导入结果不应该为空", result);
        
        // 验证每个记录的子单号类型推导
        for (int i = 0; i < testList.size(); i++) {
            AccountTradeDetail detail = testList.get(i);
            assertEquals("子单号类型推导错误，索引: " + i + ", 子单号: " + subOrderCodes[i], 
                        expectedTypes[i], detail.getSubOrderType());
        }
        
        System.out.println("✅ 子单号类型自动推导测试通过");
    }
    
    /**
     * 测试流水状态自动设置功能
     */
    @Test
    public void testFlowStatusAutoSetting() throws Exception {
        List<AccountTradeDetail> testList = new ArrayList<>();
        
        // 测试数据：有店铺信息，支持的交易类型
        AccountTradeDetail detail1 = createTestDetail("FLOW_TEST_1", "订单", "测试店铺");
        
        // 测试数据：无店铺信息，支持的交易类型
        AccountTradeDetail detail2 = createTestDetail("FLOW_TEST_2", "订单", null);
        
        // 测试数据：无店铺信息，不支持的交易类型
        AccountTradeDetail detail3 = createTestDetail("FLOW_TEST_3", "不支持的类型", null);
        
        testList.add(detail1);
        testList.add(detail2);
        testList.add(detail3);
        
        // 执行导入
        String result = accountTradeDetailService.importAccountTradeDetail(testList);
        
        // 验证结果
        assertNotNull("导入结果不应该为空", result);
        
        // 验证流水状态设置
        assertEquals("有店铺信息应该设置为成功状态", Long.valueOf(2L), detail1.getFlowStatus());
        assertEquals("无店铺信息的支持类型应该设置为未处理状态", Long.valueOf(0L), detail2.getFlowStatus());
        assertEquals("不支持的交易类型应该设置为不支持状态", Long.valueOf(3L), detail3.getFlowStatus());
        
        System.out.println("✅ 流水状态自动设置测试通过");
    }
    
    /**
     * 创建测试数据
     */
    private AccountTradeDetail createTestDetail(String serialSuffix, String tradeType, String shop) {
        AccountTradeDetail detail = new AccountTradeDetail();
        detail.setTradeSerial(serialSuffix + "_" + System.currentTimeMillis());
        detail.setPayAccount("<EMAIL>");
        detail.setIncomeType("收入");
        detail.setTradeType(tradeType);
        detail.setTradeStatus("成功");
        detail.setTradeAmount("100.00");
        detail.setCurrency("CNY");
        detail.setTradeTime(new Date());
        detail.setOrderCode("ORDER001");
        detail.setSubOrderCode("SUB001");
        detail.setOrderNo("ORDER001");
        detail.setShop(shop);
        return detail;
    }
}
