package com.ruoyi.data.service;

import com.ruoyi.data.domain.AccountTradeDetail;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

import static org.junit.Assert.*;

/**
 * 账户交易明细更新流水状态测试
 * 验证新增和更新操作中流水状态的正确设置
 * 
 * <AUTHOR>
 * @date 2025-01-28
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@Transactional
public class AccountTradeDetailUpdateFlowStatusTest {

    @Autowired
    private IAccountTradeDetailService accountTradeDetailService;

    /**
     * 测试新增记录时流水状态的设置
     */
    @Test
    public void testInsertFlowStatusSetting() {
        // 测试1：有店铺信息的新增
        AccountTradeDetail detailWithShop = createTestDetail("INSERT_WITH_SHOP", "订单", "测试店铺");
        int result1 = accountTradeDetailService.insertAccountTradeDetail(detailWithShop);
        
        assertEquals("新增应该成功", 1, result1);
        assertEquals("有店铺信息应该设置为成功状态", Long.valueOf(2L), detailWithShop.getFlowStatus());
        assertTrue("执行日志应该包含店铺信息", detailWithShop.getExecutionLog().contains("测试店铺"));
        
        // 测试2：无店铺信息的新增（支持的交易类型）
        AccountTradeDetail detailWithoutShop = createTestDetail("INSERT_WITHOUT_SHOP", "订单", null);
        int result2 = accountTradeDetailService.insertAccountTradeDetail(detailWithoutShop);
        
        assertEquals("新增应该成功", 1, result2);
        assertEquals("无店铺信息的支持类型应该设置为未处理状态", Long.valueOf(0L), detailWithoutShop.getFlowStatus());
        assertFalse("执行日志不应该包含店铺信息", detailWithoutShop.getExecutionLog().contains("店铺"));
        
        // 测试3：无店铺信息的新增（不支持的交易类型）
        AccountTradeDetail detailUnsupported = createTestDetail("INSERT_UNSUPPORTED", "不支持的类型", null);
        int result3 = accountTradeDetailService.insertAccountTradeDetail(detailUnsupported);
        
        assertEquals("新增应该成功", 1, result3);
        assertEquals("不支持的交易类型应该设置为未知状态", Long.valueOf(3L), detailUnsupported.getFlowStatus());
        
        System.out.println("✅ 新增流水状态设置测试通过");
    }
    
    /**
     * 测试更新记录时流水状态的设置
     */
    @Test
    public void testUpdateFlowStatusSetting() {
        // 先创建一个基础记录
        AccountTradeDetail baseRecord = createTestDetail("UPDATE_FLOW_TEST", "订单", null);
        baseRecord.setFlowStatus(1L); // 设置为失败状态
        accountTradeDetailService.insertAccountTradeDetail(baseRecord);
        
        // 测试1：更新时添加店铺信息
        baseRecord.setShop("新增的店铺");
        baseRecord.setExecutionLog(null); // 清空日志让系统重新生成
        int result1 = accountTradeDetailService.updateAccountTradeDetail(baseRecord);
        
        assertEquals("更新应该成功", 1, result1);
        assertEquals("添加店铺信息应该设置为成功状态", Long.valueOf(2L), baseRecord.getFlowStatus());
        assertTrue("执行日志应该包含店铺信息", baseRecord.getExecutionLog().contains("新增的店铺"));
        
        // 测试2：更新时移除店铺信息
        baseRecord.setShop(null);
        baseRecord.setExecutionLog(null); // 清空日志让系统重新生成
        int result2 = accountTradeDetailService.updateAccountTradeDetail(baseRecord);
        
        assertEquals("更新应该成功", 1, result2);
        assertEquals("移除店铺信息应该设置为未处理状态", Long.valueOf(0L), baseRecord.getFlowStatus());
        assertFalse("执行日志不应该包含店铺信息", baseRecord.getExecutionLog().contains("店铺"));
        
        System.out.println("✅ 更新流水状态设置测试通过");
    }
    
    /**
     * 测试更新记录时不同交易类型的状态设置
     */
    @Test
    public void testUpdateWithDifferentTradeTypes() {
        // 测试支持的交易类型
        AccountTradeDetail supportedRecord = createTestDetail("UPDATE_SUPPORTED", "订单", null);
        accountTradeDetailService.insertAccountTradeDetail(supportedRecord);
        
        supportedRecord.setRemark("更新备注");
        supportedRecord.setExecutionLog(null);
        accountTradeDetailService.updateAccountTradeDetail(supportedRecord);
        
        assertEquals("支持的交易类型无店铺应该设置为未处理状态", Long.valueOf(0L), supportedRecord.getFlowStatus());
        
        // 测试不支持的交易类型
        AccountTradeDetail unsupportedRecord = createTestDetail("UPDATE_UNSUPPORTED", "不支持的类型", null);
        accountTradeDetailService.insertAccountTradeDetail(unsupportedRecord);
        
        unsupportedRecord.setRemark("更新备注");
        unsupportedRecord.setExecutionLog(null);
        accountTradeDetailService.updateAccountTradeDetail(unsupportedRecord);
        
        assertEquals("不支持的交易类型应该设置为未知状态", Long.valueOf(3L), unsupportedRecord.getFlowStatus());
        
        System.out.println("✅ 不同交易类型更新测试通过");
    }
    
    /**
     * 测试更新时保持已有的成功状态
     */
    @Test
    public void testUpdateKeepSuccessStatus() {
        // 创建一个有店铺信息的成功记录
        AccountTradeDetail successRecord = createTestDetail("UPDATE_SUCCESS", "订单", "原始店铺");
        successRecord.setFlowStatus(2L);
        accountTradeDetailService.insertAccountTradeDetail(successRecord);
        
        // 更新其他字段但保持店铺信息
        successRecord.setRemark("更新备注");
        successRecord.setShop("更新后的店铺");
        successRecord.setExecutionLog(null);
        int result = accountTradeDetailService.updateAccountTradeDetail(successRecord);
        
        assertEquals("更新应该成功", 1, result);
        assertEquals("有店铺信息应该保持成功状态", Long.valueOf(2L), successRecord.getFlowStatus());
        assertEquals("店铺信息应该被更新", "更新后的店铺", successRecord.getShop());
        assertTrue("执行日志应该包含新的店铺信息", successRecord.getExecutionLog().contains("更新后的店铺"));
        
        System.out.println("✅ 保持成功状态测试通过");
    }
    
    /**
     * 创建测试记录
     */
    private AccountTradeDetail createTestDetail(String serialSuffix, String tradeType, String shop) {
        AccountTradeDetail detail = new AccountTradeDetail();
        detail.setTradeSerial(serialSuffix + "_" + System.currentTimeMillis());
        detail.setPayAccount("<EMAIL>");
        detail.setIncomeType("收入");
        detail.setTradeType(tradeType);
        detail.setTradeStatus("成功");
        detail.setTradeAmount("100.00");
        detail.setCurrency("CNY");
        detail.setTradeTime(new Date());
        detail.setAvailableAmount("1000.00");
        detail.setFrozenAmount("0.00");
        detail.setAccountAmount("1100.00");
        detail.setPendingAmount("0.00");
        detail.setDeposit("0.00");
        detail.setOrderCode("ORDER001");
        detail.setSubOrderCode("SUB001");
        detail.setRemark("测试记录");
        detail.setOrderNo("ORDER001");
        detail.setShop(shop);
        return detail;
    }
}
