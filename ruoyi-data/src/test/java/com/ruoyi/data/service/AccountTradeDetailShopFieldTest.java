package com.ruoyi.data.service;

import com.ruoyi.data.domain.AccountTradeDetail;
import com.ruoyi.data.service.impl.AccountTradeDetailServiceImpl;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

import static org.junit.Assert.*;

/**
 * 账户交易明细店铺字段功能测试
 * 
 * <AUTHOR>
 * @date 2025-01-28
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@Transactional
public class AccountTradeDetailShopFieldTest {

    @Autowired
    private IAccountTradeDetailService accountTradeDetailService;

    /**
     * 测试新增记录时有店铺字段的处理
     */
    @Test
    public void testInsertWithShopField() {
        // 创建测试数据
        AccountTradeDetail detail = new AccountTradeDetail();
        detail.setTradeSerial("TEST_SHOP_" + System.currentTimeMillis());
        detail.setPayAccount("<EMAIL>");
        detail.setIncomeType("收入");
        detail.setTradeType("订单");
        detail.setTradeStatus("成功");
        detail.setTradeAmount("100.00");
        detail.setCurrency("CNY");
        detail.setTradeTime(new Date());
        detail.setShop("测试店铺"); // 设置店铺字段
        
        // 执行新增
        int result = accountTradeDetailService.insertAccountTradeDetail(detail);
        
        // 验证结果
        assertEquals(1, result);
        assertEquals(Long.valueOf(2L), detail.getFlowStatus()); // 应该设置为成功状态
        assertNotNull(detail.getExecutionLog());
        assertTrue("执行日志应包含店铺信息", detail.getExecutionLog().contains("测试店铺"));
        assertTrue("执行日志应包含手动填写标识", detail.getExecutionLog().contains("手动填写店铺字段"));
    }

    /**
     * 测试新增记录时没有店铺字段的处理
     */
    @Test
    public void testInsertWithoutShopField() {
        // 创建测试数据
        AccountTradeDetail detail = new AccountTradeDetail();
        detail.setTradeSerial("TEST_NO_SHOP_" + System.currentTimeMillis());
        detail.setPayAccount("<EMAIL>");
        detail.setIncomeType("收入");
        detail.setTradeType("订单");
        detail.setTradeStatus("成功");
        detail.setTradeAmount("100.00");
        detail.setCurrency("CNY");
        detail.setTradeTime(new Date());
        // 不设置店铺字段
        
        // 执行新增
        int result = accountTradeDetailService.insertAccountTradeDetail(detail);
        
        // 验证结果
        assertEquals(1, result);
        assertEquals(Long.valueOf(0L), detail.getFlowStatus()); // 应该设置为未处理状态（支持的交易类型）
        assertNotNull(detail.getExecutionLog());
        assertFalse("执行日志不应包含店铺信息", detail.getExecutionLog().contains("店铺"));
        assertTrue("执行日志应包含交易流水", detail.getExecutionLog().contains(detail.getTradeSerial()));
    }

    /**
     * 测试更新记录时添加店铺字段的处理
     */
    @Test
    public void testUpdateWithShopField() {
        // 先创建一个没有店铺信息的记录
        AccountTradeDetail detail = new AccountTradeDetail();
        detail.setTradeSerial("TEST_UPDATE_SHOP_" + System.currentTimeMillis());
        detail.setPayAccount("<EMAIL>");
        detail.setIncomeType("收入");
        detail.setTradeType("订单");
        detail.setTradeStatus("成功");
        detail.setTradeAmount("100.00");
        detail.setCurrency("CNY");
        detail.setTradeTime(new Date());
        
        accountTradeDetailService.insertAccountTradeDetail(detail);
        
        // 更新记录，添加店铺信息
        detail.setShop("更新后的店铺");
        detail.setExecutionLog(null); // 清空日志，让系统重新生成
        
        int result = accountTradeDetailService.updateAccountTradeDetail(detail);
        
        // 验证结果
        assertEquals(1, result);
        assertEquals(Long.valueOf(2L), detail.getFlowStatus()); // 应该更新为成功状态
        assertNotNull(detail.getExecutionLog());
        assertTrue("执行日志应包含店铺信息", detail.getExecutionLog().contains("更新后的店铺"));
        assertTrue("执行日志应包含手动填写标识", detail.getExecutionLog().contains("手动填写店铺字段"));
    }
}
