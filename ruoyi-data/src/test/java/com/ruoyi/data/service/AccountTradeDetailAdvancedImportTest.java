package com.ruoyi.data.service;

import com.ruoyi.data.domain.AccountTradeDetail;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static org.junit.Assert.*;

/**
 * 账户交易明细高级导入逻辑测试
 * 测试基于已存在数据状态的智能更新逻辑
 * 
 * <AUTHOR>
 * @date 2025-01-28
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@Transactional
public class AccountTradeDetailAdvancedImportTest {

    @Autowired
    private IAccountTradeDetailService accountTradeDetailService;

    /**
     * 测试场景1：已存在成功记录有店铺信息，导入数据无店铺信息 -> 跳过更新
     */
    @Test
    public void testSkipUpdateWhenExistingSuccessWithShopAndImportWithoutShop() throws Exception {
        // 1. 先创建一个成功状态且有店铺信息的记录
        String tradeSerial = "SKIP_TEST_" + System.currentTimeMillis();
        AccountTradeDetail existingRecord = createTestRecord(tradeSerial, "订单", "原始店铺");
        existingRecord.setFlowStatus(2L); // 设置为成功状态
        
        accountTradeDetailService.insertAccountTradeDetail(existingRecord);
        
        // 2. 准备导入数据（无店铺信息）
        List<AccountTradeDetail> importList = new ArrayList<>();
        AccountTradeDetail importRecord = createTestRecord(tradeSerial, "订单", null);
        importRecord.setRemark("导入更新的备注");
        importList.add(importRecord);
        
        // 3. 执行导入
        String result = accountTradeDetailService.importAccountTradeDetail(importList);
        
        // 4. 验证结果
        assertNotNull("导入结果不应该为空", result);
        
        // 查询数据库中的记录，应该保持原有的店铺信息
        AccountTradeDetail dbRecord = accountTradeDetailService.selectAccountTradeDetailByTradeSerial(tradeSerial);
        assertNotNull("数据库记录不应该为空", dbRecord);
        assertEquals("店铺信息应该保持不变", "原始店铺", dbRecord.getShop());
        assertNotEquals("备注不应该被更新", "导入更新的备注", dbRecord.getRemark());
        
        System.out.println("✅ 跳过更新测试通过：" + dbRecord.getShop());
    }
    
    /**
     * 测试场景2：已存在成功记录有店铺信息，导入数据也有店铺信息 -> 更新并记录变更
     */
    @Test
    public void testUpdateWhenBothHaveShopInfo() throws Exception {
        // 1. 先创建一个成功状态且有店铺信息的记录
        String tradeSerial = "UPDATE_SHOP_TEST_" + System.currentTimeMillis();
        AccountTradeDetail existingRecord = createTestRecord(tradeSerial, "订单", "京东旗舰店");
        existingRecord.setFlowStatus(2L); // 设置为成功状态
        
        accountTradeDetailService.insertAccountTradeDetail(existingRecord);
        
        // 2. 准备导入数据（有不同的店铺信息）
        List<AccountTradeDetail> importList = new ArrayList<>();
        AccountTradeDetail importRecord = createTestRecord(tradeSerial, "订单", "天猫专营店");
        importRecord.setRemark("导入更新的备注");
        importList.add(importRecord);
        
        // 3. 执行导入
        String result = accountTradeDetailService.importAccountTradeDetail(importList);
        
        // 4. 验证结果
        assertNotNull("导入结果不应该为空", result);
        
        // 查询数据库中的记录，应该更新为新的店铺信息
        AccountTradeDetail dbRecord = accountTradeDetailService.selectAccountTradeDetailByTradeSerial(tradeSerial);
        assertNotNull("数据库记录不应该为空", dbRecord);
        assertEquals("店铺信息应该被更新", "天猫专营店", dbRecord.getShop());
        assertEquals("备注应该被更新", "导入更新的备注", dbRecord.getRemark());
        assertTrue("执行日志应该记录店铺变更", dbRecord.getExecutionLog().contains("从 京东旗舰店 变为 天猫专营店"));
        
        System.out.println("✅ 店铺变更更新测试通过：" + dbRecord.getExecutionLog());
    }
    
    /**
     * 测试场景3：已存在失败记录，导入数据有店铺信息 -> 更新并设置为成功状态
     */
    @Test
    public void testUpdateFailedRecordWithShopInfo() throws Exception {
        // 1. 先创建一个失败状态的记录
        String tradeSerial = "FAILED_UPDATE_TEST_" + System.currentTimeMillis();
        AccountTradeDetail existingRecord = createTestRecord(tradeSerial, "订单", null);
        existingRecord.setFlowStatus(1L); // 设置为失败状态
        
        accountTradeDetailService.insertAccountTradeDetail(existingRecord);
        
        // 2. 准备导入数据（有店铺信息）
        List<AccountTradeDetail> importList = new ArrayList<>();
        AccountTradeDetail importRecord = createTestRecord(tradeSerial, "订单", "拼多多店铺");
        importRecord.setRemark("失败记录重新导入");
        importList.add(importRecord);
        
        // 3. 执行导入
        String result = accountTradeDetailService.importAccountTradeDetail(importList);
        
        // 4. 验证结果
        assertNotNull("导入结果不应该为空", result);
        
        // 查询数据库中的记录，应该被更新
        AccountTradeDetail dbRecord = accountTradeDetailService.selectAccountTradeDetailByTradeSerial(tradeSerial);
        assertNotNull("数据库记录不应该为空", dbRecord);
        assertEquals("店铺信息应该被设置", "拼多多店铺", dbRecord.getShop());
        assertEquals("备注应该被更新", "失败记录重新导入", dbRecord.getRemark());
        assertEquals("流水状态应该被设置为成功", Long.valueOf(2L), dbRecord.getFlowStatus());
        assertTrue("执行日志应该记录新增店铺", dbRecord.getExecutionLog().contains("新增店铺: 拼多多店铺"));
        
        System.out.println("✅ 失败记录更新测试通过：" + dbRecord.getExecutionLog());
    }
    
    /**
     * 测试场景4：已存在记录无店铺信息，导入数据有店铺信息 -> 更新并添加店铺
     */
    @Test
    public void testUpdateRecordWithoutShopToWithShop() throws Exception {
        // 1. 先创建一个无店铺信息的记录
        String tradeSerial = "ADD_SHOP_TEST_" + System.currentTimeMillis();
        AccountTradeDetail existingRecord = createTestRecord(tradeSerial, "订单", null);
        existingRecord.setFlowStatus(0L); // 设置为未处理状态
        
        accountTradeDetailService.insertAccountTradeDetail(existingRecord);
        
        // 2. 准备导入数据（有店铺信息）
        List<AccountTradeDetail> importList = new ArrayList<>();
        AccountTradeDetail importRecord = createTestRecord(tradeSerial, "订单", "新增店铺");
        importRecord.setRemark("添加店铺信息");
        importList.add(importRecord);
        
        // 3. 执行导入
        String result = accountTradeDetailService.importAccountTradeDetail(importList);
        
        // 4. 验证结果
        assertNotNull("导入结果不应该为空", result);
        
        // 查询数据库中的记录，应该被更新
        AccountTradeDetail dbRecord = accountTradeDetailService.selectAccountTradeDetailByTradeSerial(tradeSerial);
        assertNotNull("数据库记录不应该为空", dbRecord);
        assertEquals("店铺信息应该被设置", "新增店铺", dbRecord.getShop());
        assertEquals("备注应该被更新", "添加店铺信息", dbRecord.getRemark());
        assertEquals("流水状态应该被设置为成功", Long.valueOf(2L), dbRecord.getFlowStatus());
        assertTrue("执行日志应该记录新增店铺", dbRecord.getExecutionLog().contains("新增店铺: 新增店铺"));
        
        System.out.println("✅ 添加店铺信息测试通过：" + dbRecord.getExecutionLog());
    }
    
    /**
     * 创建测试记录
     */
    private AccountTradeDetail createTestRecord(String tradeSerial, String tradeType, String shop) {
        AccountTradeDetail detail = new AccountTradeDetail();
        detail.setTradeSerial(tradeSerial);
        detail.setPayAccount("<EMAIL>");
        detail.setIncomeType("收入");
        detail.setTradeType(tradeType);
        detail.setTradeStatus("成功");
        detail.setTradeAmount("100.00");
        detail.setCurrency("CNY");
        detail.setTradeTime(new Date());
        detail.setAvailableAmount("1000.00");
        detail.setFrozenAmount("0.00");
        detail.setAccountAmount("1100.00");
        detail.setPendingAmount("0.00");
        detail.setDeposit("0.00");
        detail.setOrderCode("ORDER001");
        detail.setSubOrderCode("SUB001");
        detail.setRemark("测试记录");
        detail.setOrderNo("ORDER001");
        detail.setShop(shop);
        return detail;
    }
}
