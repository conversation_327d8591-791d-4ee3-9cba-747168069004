package com.ruoyi.system.service;

import java.io.IOException;
import java.util.List;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.system.domain.req.ReqStepAttachment;

/**
 * 步骤附件Service接口
 */
public interface IReqStepAttachmentService 
{
    /**
     * 查询步骤附件
     * 
     * @param attachmentId 步骤附件主键
     * @return 步骤附件
     */
    public ReqStepAttachment selectReqStepAttachmentByAttachmentId(Long attachmentId);

    /**
     * 查询步骤附件列表
     * 
     * @param reqStepAttachment 步骤附件
     * @return 步骤附件集合
     */
    public List<ReqStepAttachment> selectReqStepAttachmentList(ReqStepAttachment reqStepAttachment);
    
    /**
     * 根据步骤ID查询附件列表
     *
     * @param stepId 步骤ID
     * @return 附件列表
     */
    public List<ReqStepAttachment> selectReqStepAttachmentByStepId(Long stepId);

    /**
     * 上传步骤附件到本地
     *
     * @param file 文件
     * @param stepId 步骤ID
     * @return 上传结果
     * @throws IOException IO异常
     */
    public ReqStepAttachment uploadStepAttachment(MultipartFile file, Long stepId) throws IOException;

    /**
     * 上传步骤附件到OSS
     *
     * @param file 文件
     * @param stepId 步骤ID
     * @return 上传结果
     */
    public ReqStepAttachment uploadStepAttachmentToOss(MultipartFile file, Long stepId) throws Exception;

    /**
     * 新增步骤附件
     * 
     * @param reqStepAttachment 步骤附件
     * @return 结果
     */
    public int insertReqStepAttachment(ReqStepAttachment reqStepAttachment);

    /**
     * 修改步骤附件
     * 
     * @param reqStepAttachment 步骤附件
     * @return 结果
     */
    public int updateReqStepAttachment(ReqStepAttachment reqStepAttachment);

    /**
     * 批量删除步骤附件
     * 
     * @param attachmentIds 需要删除的步骤附件主键集合
     * @return 结果
     */
    public int deleteReqStepAttachmentByAttachmentIds(Long[] attachmentIds);

    /**
     * 删除步骤附件信息
     * 
     * @param attachmentId 步骤附件主键
     * @return 结果
     */
    public int deleteReqStepAttachmentByAttachmentId(Long attachmentId);
    
    /**
     * 根据步骤ID删除附件
     *
     * @param stepId 步骤ID
     * @return 结果
     */
    public int deleteReqStepAttachmentByStepId(Long stepId);
} 