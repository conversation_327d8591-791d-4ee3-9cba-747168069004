{"configurations": [{"request": "launch", "env": {}, "mainClass": "com.ruoyi.RuoYiApplication", "console": "integratedTerminal", "name": "Spring Boot - com.ruoyi.RuoYiApplication", "type": "java", "projectName": "backendAndFront", "args": "", "vmArgs": "-Dspring.profiles.active=dev", "internalConsoleOptions": "neverOpen"}, {"request": "launch", "mainClass": "com.ruoyi.common.utils.html.EscapeUtil", "console": "integratedTerminal", "name": "Launch com.ruoyi.common.utils.html.EscapeUtil", "type": "java", "projectName": "backendAndFront", "args": "", "internalConsoleOptions": "neverOpen"}, {"request": "launch", "env": {}, "mainClass": "com.ruoyi.data.DataTestApplication", "console": "integratedTerminal", "name": "Spring Boot - com.ruoyi.data.DataTestApplication", "type": "java", "projectName": "backendAndFront", "args": "", "vmArgs": "-Dspring.profiles.active=dev", "internalConsoleOptions": "neverOpen"}, {"request": "launch", "mainClass": "com.ruoyi.yimai.aimodel.test.ApiTest", "console": "integratedTerminal", "name": "Launch com.ruoyi.yimai.aimodel.test.ApiTest", "type": "java", "projectName": "backendAndFront", "args": "", "internalConsoleOptions": "neverOpen"}, {"request": "launch", "mainClass": "com.ruoyi.yimai.aimodel.test.CharacterTest", "console": "integratedTerminal", "name": "Launch com.ruoyi.yimai.aimodel.test.CharacterTest", "type": "java", "projectName": "backendAndFront", "args": "", "internalConsoleOptions": "neverOpen"}, {"request": "launch", "mainClass": "com.ruoyi.yimai.aimodel.test.JsonParseTest", "console": "integratedTerminal", "name": "Launch com.ruoyi.yimai.aimodel.test.JsonParseTest", "type": "java", "projectName": "backendAndFront", "args": "", "internalConsoleOptions": "neverOpen"}, {"request": "launch", "mainClass": "com.ruoyi.yimai.aimodel.test.MarkdownJsonTest", "console": "integratedTerminal", "name": "Launch com.ruoyi.yimai.aimodel.test.MarkdownJsonTest", "type": "java", "projectName": "backendAndFront", "args": "", "internalConsoleOptions": "neverOpen"}, {"request": "launch", "mainClass": "com.ruoyi.yimai.aimodel.test.SimpleApiTest", "console": "integratedTerminal", "name": "Launch com.ruoyi.yimai.aimodel.test.SimpleApiTest", "type": "java", "projectName": "backendAndFront", "args": "", "internalConsoleOptions": "neverOpen"}], "version": "0.2.0"}