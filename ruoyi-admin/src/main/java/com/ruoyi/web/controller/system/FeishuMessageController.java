package com.ruoyi.web.controller.system;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.feishu.FeishuMessageUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 飞书消息发送 控制器
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/feishu")
public class FeishuMessageController extends BaseController {

    /**
     * 发送文本消息
     */
    @Log(title = "飞书消息", businessType = BusinessType.OTHER)
    @PostMapping("/sendText")
    public AjaxResult sendText(@RequestBody FeishuTextMessageDTO messageDTO) {
        boolean result = FeishuMessageUtils.sendTextMessage(messageDTO.getWebhookKey(), messageDTO.getContent());
        return result ? success("发送成功") : error("发送失败");
    }

    /**
     * 发送富文本消息
     */
    @Log(title = "飞书消息", businessType = BusinessType.OTHER)
    @PostMapping("/sendRichText")
    public AjaxResult sendRichText(@RequestBody FeishuRichTextMessageDTO messageDTO) {
        boolean result = FeishuMessageUtils.sendRichTextMessage(messageDTO.getWebhookKey(), 
                messageDTO.getTitle(), messageDTO.getContent());
        return result ? success("发送成功") : error("发送失败");
    }

    /**
     * 飞书文本消息请求DTO
     */
    public static class FeishuTextMessageDTO {
        /** webhook key */
        private String webhookKey;
        /** 消息内容 */
        private String content;

        public String getWebhookKey() {
            return webhookKey;
        }

        public void setWebhookKey(String webhookKey) {
            this.webhookKey = webhookKey;
        }

        public String getContent() {
            return content;
        }

        public void setContent(String content) {
            this.content = content;
        }
    }

    /**
     * 飞书富文本消息请求DTO
     */
    public static class FeishuRichTextMessageDTO {
        /** webhook key */
        private String webhookKey;
        /** 标题 */
        private String title;
        /** 消息内容 */
        private String content;

        public String getWebhookKey() {
            return webhookKey;
        }

        public void setWebhookKey(String webhookKey) {
            this.webhookKey = webhookKey;
        }

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getContent() {
            return content;
        }

        public void setContent(String content) {
            this.content = content;
        }
    }
} 