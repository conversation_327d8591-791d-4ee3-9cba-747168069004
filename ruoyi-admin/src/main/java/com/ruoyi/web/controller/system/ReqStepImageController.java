package com.ruoyi.web.controller.system;

import java.util.List;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.req.ReqStepImage;
import com.ruoyi.system.service.IReqStepImageService;

/**
 * 步骤图片Controller
 * 
 */
@RestController
@RequestMapping("/requirement/image")
public class ReqStepImageController extends BaseController
{
    @Autowired
    private IReqStepImageService reqStepImageService;

    /**
     * 查询步骤下的所有图片
     */
    @PreAuthorize("@ss.hasPermi('requirement:demand:query')")
    @GetMapping(value = "/step/{stepId}")
    public AjaxResult getImagesByStepId(@PathVariable("stepId") Long stepId)
    {
        List<ReqStepImage> list = reqStepImageService.selectReqStepImageByStepId(stepId);
        return success(list);
    }

    /**
     * 获取步骤图片详细信息
     */
    @PreAuthorize("@ss.hasPermi('requirement:demand:query')")
    @GetMapping(value = "/{imageId}")
    public AjaxResult getInfo(@PathVariable("imageId") Long imageId)
    {
        return success(reqStepImageService.selectReqStepImageByImageId(imageId));
    }

    /**
     * 上传步骤图片
     */
    @PreAuthorize("@ss.hasPermi('requirement:demand:edit')")
    @Log(title = "步骤图片", businessType = BusinessType.INSERT)
    @PostMapping("/upload")
    public AjaxResult upload(@RequestParam("file") MultipartFile file, @RequestParam("stepId") Long stepId)
    {
        try {
            // 调用阿里云OSS上传服务
            ReqStepImage image = reqStepImageService.uploadStepImageToOss(file, stepId);
            return success(image);
        }
        catch (Exception e) {
            return error(e.getMessage());
        }
    }

    /**
     * 删除步骤图片
     */
    @PreAuthorize("@ss.hasPermi('requirement:demand:edit')")
    @Log(title = "步骤图片", businessType = BusinessType.DELETE)
    @DeleteMapping("/{imageIds}")
    public AjaxResult remove(@PathVariable Long[] imageIds)
    {
        return toAjax(reqStepImageService.deleteReqStepImageByImageIds(imageIds));
    }
} 