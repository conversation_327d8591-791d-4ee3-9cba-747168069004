<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="文件名" prop="fileName">
        <el-input
          v-model="queryParams.fileName"
          placeholder="请输入文件名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="文件类型" prop="fileType">
        <el-select v-model="queryParams.fileType" placeholder="请选择文件类型" clearable>
          <el-option
            v-for="dict in fileTypeOptions"
            :key="dict.dictValue"
            :label="dict.dictLabel"
            :value="dict.dictValue"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="上传人" prop="createBy">
        <el-input
          v-model="queryParams.createBy"
          placeholder="请输入上传人"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="上传时间">
        <el-date-picker
          v-model="dateRange"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-upload"
          size="mini"
          @click="handleUpload"
          v-hasPermi="['yimai:filemanager:upload']"
        >上传文件</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['yimai:filemanager:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['yimai:filemanager:export']"
        >导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-s-operation"
          size="mini"
          @click="handleExecuteTemplate"
          v-hasPermi="['yimai:filemanager:execute']"
        >根据模版执行</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-setting"
          size="mini"
          @click="handleTemplateMethodManage"
          v-hasPermi="['yimai:filemanager:execute']"
        >模板方法管理</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="fileList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="文件ID" align="center" prop="fileId" v-if="false" />
      <el-table-column label="文件名" align="center" prop="fileName" :show-overflow-tooltip="true" />
      <el-table-column label="文件类型" align="center" prop="fileType">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.fileType === 'image'">图片</el-tag>
          <el-tag v-else-if="scope.row.fileType === 'document'" type="success">文档</el-tag>
          <el-tag v-else-if="scope.row.fileType === 'video'" type="warning">视频</el-tag>
          <el-tag v-else-if="scope.row.fileType === 'audio'" type="info">音频</el-tag>
          <el-tag v-else-if="scope.row.fileType === 'other'" type="danger">其他</el-tag>
          <span v-else>{{ scope.row.fileType }}</span>
        </template>
      </el-table-column>
      <el-table-column label="文件大小" align="center" prop="fileSize">
        <template slot-scope="scope">
          {{ formatFileSize(scope.row.fileSize) }}
        </template>
      </el-table-column>
      <el-table-column label="预览" align="center" width="80">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handlePreview(scope.row)"
            v-if="isPreviewable(scope.row.fileType)"
          >预览</el-button>
        </template>
      </el-table-column>
      <el-table-column label="上传人" align="center" prop="createBy" width="100" />
      <el-table-column label="上传时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="备注" align="center" prop="remark" :show-overflow-tooltip="true" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-download"
            @click="handleDownload(scope.row)"
            v-hasPermi="['yimai:filemanager:download']"
          >下载</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['yimai:filemanager:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 上传文件对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="500px" append-to-body>
      <el-form ref="uploadForm" :model="upload.form" :rules="upload.rules" label-width="80px">
        <el-form-item label="文件分类" prop="category">
          <el-select v-model="upload.form.category" placeholder="请选择文件分类">
            <el-option
              v-for="dict in fileCategoryOptions"
              :key="dict.dictValue"
              :label="dict.dictLabel"
              :value="dict.dictValue"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="upload.form.remark" type="textarea" placeholder="请输入备注"></el-input>
        </el-form-item>
        <el-form-item label="文件" prop="fileList">
          <el-upload
            ref="upload"
            :limit="5"
            :action="uploadFileUrl"
            :headers="headers"
            :file-list="upload.form.fileList"
            :on-progress="handleFileUploadProgress"
            :on-success="handleFileSuccess"
            :on-error="handleFileError"
            :on-exceed="handleExceed"
            :before-upload="beforeFileUpload"
            :on-remove="handleFileRemove"
            multiple
            :drag="true"
          >
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
            <div class="el-upload__tip" slot="tip">
              支持jpg、png、gif、doc、docx、xls、xlsx、pdf等多种格式文件，且文件大小不超过100MB
            </div>
          </el-upload>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm" :loading="upload.loading">确 定</el-button>
        <el-button @click="cancelUpload">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 文件预览对话框 -->
    <el-dialog title="文件预览" :visible.sync="preview.open" width="800px" append-to-body>
      <div v-if="preview.type === 'image'" class="preview-container">
        <img :src="preview.url" style="max-width: 100%; max-height: 70vh;" />
      </div>
      <div v-else-if="preview.type === 'pdf'" class="preview-container">
        <iframe :src="preview.url" width="100%" height="500px"></iframe>
      </div>
      <div v-else-if="preview.type === 'video'" class="preview-container">
        <video :src="preview.url" controls style="max-width: 100%;"></video>
      </div>
      <div v-else-if="preview.type === 'audio'" class="preview-container">
        <audio :src="preview.url" controls style="width: 100%;"></audio>
      </div>
      <div v-else class="preview-container">
        <p>该文件类型不支持在线预览，请下载后查看</p>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="handleDownload(preview.data)" icon="el-icon-download">下载文件</el-button>
        <el-button @click="preview.open = false">关 闭</el-button>
      </div>
    </el-dialog>

    <!-- 根据模版执行对话框 -->
    <el-dialog title="根据模版执行" :visible.sync="executeTemplate.open" width="500px" append-to-body>
      <el-form ref="templateForm" :model="executeTemplate.form" :rules="executeTemplate.rules" label-width="100px">
        <el-form-item label="执行方法" prop="methodName">
          <el-select 
            v-model="executeTemplate.form.methodName" 
            placeholder="请选择执行方法" 
            filterable
            @change="handleMethodChange"
          >
            <el-option
              v-for="item in methodOptions"
              :key="item.methodName"
              :label="item.methodDesc"
              :value="item.methodName"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item v-if="selectedMethod && selectedMethod.remark">
          <div class="method-description">
            <span>{{ selectedMethod.remark }}</span>
          </div>
        </el-form-item>
        <template v-if="selectedMethod">
          <el-form-item 
            v-for="(fileReq, index) in methodFileRequirements" 
            :key="index"
            :label="fileReq.fileDesc" 
            :prop="`fileIds[${index}]`"
            :rules="[{ required: fileReq.isRequired === 'Y', message: '请选择文件', trigger: 'change' }]"
          >
            <el-select 
              v-model="executeTemplate.form.fileIds[index]" 
              placeholder="请选择文件" 
              filterable
            >
              <el-option
                v-for="item in filteredFileOptions(fileReq)"
                :key="item.fileId"
                :label="item.fileName"
                :value="item.fileId"
              ></el-option>
            </el-select>
            <span v-if="fileReq.remark" class="file-req-help">
              <el-tooltip :content="fileReq.remark" placement="top">
                <i class="el-icon-question"></i>
              </el-tooltip>
            </span>
          </el-form-item>
        </template>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="executeTemplate.form.remark" type="textarea" placeholder="请输入备注"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitTemplateExecution" :loading="executeTemplate.loading">执 行</el-button>
        <el-button @click="executeTemplate.open = false">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 模板方法管理对话框 -->
    <el-dialog title="模板方法管理" :visible.sync="templateMethodManage.open" width="900px" append-to-body>
      <el-tabs v-model="templateMethodManage.activeTab">
        <el-tab-pane label="模板方法列表" name="list">
          <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
              <el-button
                type="primary"
                plain
                icon="el-icon-plus"
                size="mini"
                @click="handleAddMethod"
              >新增</el-button>
            </el-col>
          </el-row>

          <el-table v-loading="templateMethodManage.loading" :data="templateMethodManage.methodList">
            <el-table-column label="方法ID" align="center" prop="methodId" width="80" />
            <el-table-column label="方法名称" align="center" prop="methodName" :show-overflow-tooltip="true" />
            <el-table-column label="方法描述" align="center" prop="methodDesc" :show-overflow-tooltip="true" />
            <el-table-column label="处理类名" align="center" prop="handlerClass" :show-overflow-tooltip="true" />
            <el-table-column label="排序" align="center" prop="orderNum" width="80" />
            <el-table-column label="状态" align="center" prop="status" width="80">
              <template slot-scope="scope">
                <el-switch
                  v-model="scope.row.status"
                  active-value="0"
                  inactive-value="1"
                  @change="handleMethodStatusChange(scope.row)"
                ></el-switch>
              </template>
            </el-table-column>
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
              <template slot-scope="scope">
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-edit"
                  @click="handleEditMethod(scope.row)"
                >修改</el-button>
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-delete"
                  @click="handleDeleteMethod(scope.row)"
                >删除</el-button>
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-setting"
                  @click="handleFileRequirements(scope.row)"
                >配置文件需求</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
      </el-tabs>
    </el-dialog>

    <!-- 添加或修改模板方法对话框 -->
    <el-dialog :title="templateMethodManage.title" :visible.sync="templateMethodManage.formOpen" width="500px" append-to-body>
      <el-form ref="methodForm" :model="templateMethodManage.form" :rules="templateMethodManage.rules" label-width="80px">
        <el-form-item label="方法名称" prop="methodName">
          <el-input v-model="templateMethodManage.form.methodName" placeholder="请输入方法名称" />
        </el-form-item>
        <el-form-item label="方法描述" prop="methodDesc">
          <el-input v-model="templateMethodManage.form.methodDesc" placeholder="请输入方法描述" />
        </el-form-item>
        <el-form-item label="处理类名" prop="handlerClass">
          <el-input v-model="templateMethodManage.form.handlerClass" placeholder="请输入完整的类名，包括包名" />
        </el-form-item>
        <el-form-item label="排序" prop="orderNum">
          <el-input-number v-model="templateMethodManage.form.orderNum" :min="0" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="templateMethodManage.form.status">
            <el-radio
              v-for="dict in statusOptions"
              :key="dict.dictValue"
              :label="dict.dictValue"
            >{{dict.dictLabel}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="templateMethodManage.form.remark" type="textarea" placeholder="请输入内容"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitMethodForm">确 定</el-button>
        <el-button @click="cancelMethod">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 文件需求配置对话框 -->
    <el-dialog :title="'文件需求配置 - ' + fileReqForm.methodName" :visible.sync="fileReqOpen" width="700px" append-to-body>
      <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAddFileReq">添加文件需求</el-button>
      
      <el-table :data="fileReqList" style="margin-top: 10px;">
        <el-table-column label="序号" width="60" align="center">
          <template slot-scope="scope">{{ scope.$index + 1 }}</template>
        </el-table-column>
        <el-table-column label="文件索引" prop="fileIndex" width="80" align="center" />
        <el-table-column label="文件描述" prop="fileDesc" :show-overflow-tooltip="true" />
        <el-table-column label="文件类型" prop="fileType" width="100" align="center" />
        <el-table-column label="文件来源" prop="fileSource" width="100" align="center">
          <template slot-scope="scope">
            <el-tag type="success" v-if="scope.row.fileSource === 'S'">系统存储</el-tag>
            <el-tag type="warning" v-else-if="scope.row.fileSource === 'U'">用户上传</el-tag>
            <el-tag v-else>未知</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="是否必需" prop="isRequired" width="100" align="center">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.isRequired === 'Y'">是</el-tag>
            <el-tag type="info" v-else>否</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center">
          <template slot-scope="scope">
            <el-button size="mini" type="text" icon="el-icon-edit" @click="handleEditFileReq(scope.row)">编辑</el-button>
            <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDeleteFileReq(scope.$index)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 文件需求表单 -->
      <el-dialog
        width="500px"
        :title="fileReqDialogTitle"
        :visible.sync="fileReqFormOpen"
        append-to-body
      >
        <el-form ref="fileReqForm" :model="currentFileReq" :rules="fileReqRules" label-width="100px">
          <el-form-item label="文件索引" prop="fileIndex">
            <el-input-number v-model="currentFileReq.fileIndex" :min="0" controls-position="right" />
          </el-form-item>
          <el-form-item label="文件描述" prop="fileDesc">
            <el-input v-model="currentFileReq.fileDesc" placeholder="请输入文件描述" />
          </el-form-item>
          <el-form-item label="文件类型" prop="fileType">
            <el-select v-model="currentFileReq.fileType" placeholder="请选择文件类型">
              <el-option label="Excel文件" value="excel" />
              <el-option label="CSV文件" value="csv" />
              <el-option label="文本文件" value="text" />
              <el-option label="图像文件" value="image" />
              <el-option label="其他类型" value="other" />
            </el-select>
          </el-form-item>
          <el-form-item label="文件来源" prop="fileSource">
            <el-radio-group v-model="currentFileReq.fileSource">
              <el-radio label="S">系统存储</el-radio>
              <el-radio label="U">用户上传</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="是否必需" prop="isRequired">
            <el-radio-group v-model="currentFileReq.isRequired">
              <el-radio label="Y">是</el-radio>
              <el-radio label="N">否</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="匹配模式" prop="filePattern">
            <el-input v-model="currentFileReq.filePattern" placeholder="请输入文件名匹配模式，如: .*\.xlsx?" />
          </el-form-item>
          <el-form-item label="备注" prop="remark">
            <el-input v-model="currentFileReq.remark" type="textarea" placeholder="请输入备注" />
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="submitFileReqForm">确 定</el-button>
          <el-button @click="cancelFileReq">取 消</el-button>
        </div>
      </el-dialog>

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileReqList">保 存</el-button>
        <el-button @click="fileReqOpen = false">关 闭</el-button>
      </div>
    </el-dialog>

    <!-- 执行结果预览对话框 -->
    <el-dialog title="执行结果" :visible.sync="executeResult.open" width="800px" append-to-body>
      <div v-if="executeResult.type === 'json'" class="result-container">
        <pre>{{ formatJson(executeResult.data) }}</pre>
      </div>
      <div v-else-if="executeResult.type === 'text'" class="result-container">
        <pre>{{ executeResult.data }}</pre>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="executeResult.open = false">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listFiles, getFile, delFile, addFile, updateFile, downloadFile, executeTemplateMethod, listTemplateMethods, saveTemplateMethod, getMethodFileRequirements } from "@/api/yimai/filemanager";
import { getToken } from "@/utils/auth";

export default {
  name: "FileManager",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 文件表格数据
      fileList: [],
      // 弹出层标题
      title: "",
      // 日期范围
      dateRange: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        fileName: null,
        fileType: null,
        createBy: null
      },
      // 表单参数
      form: {},
      // 上传参数
      upload: {
        open: false,
        loading: false,
        title: "上传文件",
        form: {
          category: undefined,
          remark: '',
          fileList: []
        },
        rules: {
          category: [
            { required: true, message: "请选择文件分类", trigger: "change" }
          ]
        }
      },
      // 预览参数
      preview: {
        open: false,
        url: '',
        type: '',
        data: null
      },
      // 文件类型选项
      fileTypeOptions: [
        {
          dictLabel: "图片",
          dictValue: "image"
        },
        {
          dictLabel: "文档",
          dictValue: "document"
        },
        {
          dictLabel: "视频",
          dictValue: "video"
        },
        {
          dictLabel: "音频",
          dictValue: "audio"
        },
        {
          dictLabel: "其他",
          dictValue: "other"
        }
      ],
      // 文件分类选项
      fileCategoryOptions: [
        {
          dictLabel: "店铺资料",
          dictValue: "shop"
        },
        {
          dictLabel: "客户资料",
          dictValue: "customer"
        },
        {
          dictLabel: "合同文档",
          dictValue: "contract"
        },
        {
          dictLabel: "产品图片",
          dictValue: "product"
        },
        {
          dictLabel: "其他资料",
          dictValue: "other"
        }
      ],
      // 上传文件地址
      uploadFileUrl: process.env.VUE_APP_BASE_API + "/yimai/filemanager/upload",
      // 上传文件请求头
      headers: {
        Authorization: "Bearer " + getToken()
      },
      // 根据模版执行参数
      executeTemplate: {
        open: false,
        loading: false,
        form: {
          fileIds: [],
          methodName: undefined,
          remark: ''
        },
        rules: {
          methodName: [
            { required: true, message: "请选择执行方法", trigger: "change" }
          ]
        }
      },
      // 选中的方法
      selectedMethod: null,
      // 文件选项
      fileOptions: [],
      // 方法选项
      methodOptions: [],
      // 模板方法管理参数
      templateMethodManage: {
        open: false,
        loading: true,
        activeTab: 'list',
        methodList: [],
        title: '模板方法管理',
        formOpen: false,
        form: {
          methodId: undefined,
          methodName: undefined,
          methodDesc: undefined,
          handlerClass: undefined,
          orderNum: 0,
          status: "0",
          remark: undefined
        },
        rules: {
          methodName: [
            { required: true, message: "方法名称不能为空", trigger: "blur" }
          ],
          methodDesc: [
            { required: true, message: "方法描述不能为空", trigger: "blur" }
          ],
          handlerClass: [
            { required: true, message: "处理类名不能为空", trigger: "blur" }
          ]
        },
      },
      // 文件需求配置参数
      fileReqOpen: false,
      fileReqList: [],
      fileReqFormOpen: false,
      fileReqDialogTitle: '',
      currentFileReq: {},
      fileReqEditIndex: -1,
      fileReqForm: {
        methodId: undefined,
        methodName: ''
      },
      fileReqRules: {
        fileDesc: [
          { required: true, message: "文件描述不能为空", trigger: "blur" }
        ],
        fileType: [
          { required: true, message: "文件类型不能为空", trigger: "change" }
        ],
        fileSource: [
          { required: true, message: "文件来源不能为空", trigger: "change" }
        ],
        isRequired: [
          { required: true, message: "是否必需不能为空", trigger: "change" }
        ]
      },
      // 方法的文件需求
      methodFileRequirements: [],
      // 执行结果预览
      executeResult: {
        open: false,
        type: 'text',
        data: null
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询文件列表 */
    getList() {
      this.loading = true;
      listFiles(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
        this.fileList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        fileId: null,
        fileName: null,
        filePath: null,
        fileType: null,
        fileSize: null,
        remark: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.dateRange = [];
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.fileId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 上传按钮操作 */
    handleUpload() {
      this.upload.open = true;
      this.upload.form = {
        category: undefined,
        remark: '',
        fileList: []
      };
    },
    // 文件上传中处理
    handleFileUploadProgress() {
      this.upload.loading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.loading = false;
      if (response.code === 200) {
        this.upload.form.fileList = fileList;
        this.$message.success("上传成功");
      } else {
        this.$message.error(response.msg || "上传失败");
        this.$refs.upload.handleRemove(file);
      }
    },
    // 文件上传失败处理
    handleFileError() {
      this.upload.loading = false;
      this.$message.error("上传失败，请重试");
    },
    // 文件大小超出限制处理
    handleExceed() {
      this.$message.warning("最多只能上传5个文件");
    },
    // 提交上传文件
    submitFileForm() {
      if (this.upload.form.fileList.length === 0) {
        this.$message.warning("请先上传文件");
        return;
      }
      
      this.$refs["uploadForm"].validate(valid => {
        if (valid) {
          this.upload.loading = true;
          
          const formData = {
            category: this.upload.form.category,
            remark: this.upload.form.remark,
            fileIds: this.upload.form.fileList.map(file => file.response ? file.response.fileId : null).filter(id => id)
          };
          
          addFile(formData).then(response => {
            this.$modal.msgSuccess("添加成功");
            this.upload.open = false;
            this.getList();
          }).finally(() => {
            this.upload.loading = false;
          });
        }
      });
    },
    // 文件删除处理
    handleFileRemove(file) {
      const index = this.upload.form.fileList.indexOf(file);
      if (index !== -1) {
        this.upload.form.fileList.splice(index, 1);
      }
    },
    // 取消上传
    cancelUpload() {
      this.upload.open = false;
      this.upload.loading = false;
    },
    // 上传前检查文件
    beforeFileUpload(file) {
      const isLt100M = file.size / 1024 / 1024 < 100;
      if (!isLt100M) {
        this.$message.error("文件大小不能超过100MB");
        return false;
      }
      return true;
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const fileIds = row.fileId || this.ids;
      this.$modal.confirm('是否确认删除文件编号为"' + fileIds + '"的数据项?').then(function() {
        return delFile(fileIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('yimai/filemanager/export', {
        ...this.queryParams
      }, `文件列表_${new Date().getTime()}.xlsx`);
    },
    /** 下载按钮操作 */
    handleDownload(row) {
      const fileId = row.fileId;
      downloadFile(fileId).then(response => {
        if (response.type === 'application/json') {
          // 如果返回的是JSON，说明出错了
          const reader = new FileReader();
          reader.onload = e => {
            const error = JSON.parse(e.target.result);
            this.$message.error(error.msg || '下载失败');
          };
          reader.readAsText(response);
        } else {
          // 正常下载文件
          const blob = new Blob([response]);
          const fileName = row.fileName;
          if ('download' in document.createElement('a')) {
            const link = document.createElement('a');
            link.download = fileName;
            link.style.display = 'none';
            link.href = URL.createObjectURL(blob);
            document.body.appendChild(link);
            link.click();
            URL.revokeObjectURL(link.href);
            document.body.removeChild(link);
          } else {
            navigator.msSaveBlob(blob, fileName);
          }
        }
      });
    },
    /** 预览按钮操作 */
    handlePreview(row) {
      this.preview.data = row;
      this.preview.url = row.filePath;
      this.preview.type = this.getPreviewType(row.fileType);
      this.preview.open = true;
    },
    /** 获取预览类型 */
    getPreviewType(fileType) {
      if (fileType === 'image') {
        return 'image';
      } else if (fileType === 'pdf') {
        return 'pdf';
      } else if (fileType === 'video') {
        return 'video';
      } else if (fileType === 'audio') {
        return 'audio';
      }
      return 'other';
    },
    /** 判断文件是否可预览 */
    isPreviewable(fileType) {
      return ['image', 'pdf', 'video', 'audio'].includes(fileType);
    },
    /** 格式化文件大小 */
    formatFileSize(size) {
      if (!size) return '0 B';
      
      const units = ['B', 'KB', 'MB', 'GB', 'TB'];
      let index = 0;
      while (size >= 1024 && index < units.length - 1) {
        size /= 1024;
        index++;
      }
      
      return size.toFixed(2) + ' ' + units[index];
    },
    /** 执行模版按钮操作 */
    handleExecuteTemplate() {
      this.executeTemplate.open = true;
      this.executeTemplate.form = {
        fileIds: [],
        methodName: undefined,
        remark: ''
      };
      this.selectedMethod = null;
      
      // 加载可执行方法列表
      this.loadMethodOptions();
    },
    
    /** 加载文件选项 */
    loadFileOptions() {
      // 加载所有文件作为选项
      this.loading = true;
      listFiles({
        pageNum: 1,
        pageSize: 200  // 加载更多文件以供选择
      }).then(response => {
        this.fileOptions = response.rows;
        this.loading = false;
      });
    },
    
    /** 加载方法选项 */
    loadMethodOptions() {
      listTemplateMethods().then(response => {
        this.methodOptions = response.data;
        
        // 确保文件选项已加载
        if (this.fileOptions.length === 0) {
          this.loadFileOptions();
        }
      });
    },
    
    /** 方法选择变更处理 */
    handleMethodChange(methodName) {
      // 找到选中的方法信息
      this.selectedMethod = this.methodOptions.find(m => m.methodName === methodName);
      
      // 重置文件ID数组
      this.executeTemplate.form.fileIds = [];
      
      // 获取方法的文件需求
      if (this.selectedMethod) {
        getMethodFileRequirements(this.selectedMethod.methodId).then(response => {
          this.methodFileRequirements = response.data || [];
          this.executeTemplate.form.fileIds = new Array(this.methodFileRequirements.length).fill(undefined);
        });
      } else {
        this.methodFileRequirements = [];
      }
    },
    
    /** 过滤符合文件需求的文件选项 */
    filteredFileOptions(fileReq) {
      if (!fileReq || !fileReq.filePattern) {
        return this.fileOptions;
      }
      
      // 根据文件模式过滤文件
      const regexPattern = new RegExp(fileReq.filePattern);
      return this.fileOptions.filter(file => regexPattern.test(file.fileName));
    },
    
    /** 提交模版执行 */
    submitTemplateExecution() {
      this.$refs["templateForm"].validate(valid => {
        if (valid) {
          this.executeTemplate.loading = true;
          
          // 准备请求参数
          const params = {
            methodId: this.selectedMethod ? this.selectedMethod.methodId : null,
            files: this.executeTemplate.form.fileIds.filter(id => id !== undefined),
            remark: this.executeTemplate.form.remark
          };
          
          executeTemplateMethod(params).then(response => {
            if (response && response.code === 200) {
              this.$modal.msgSuccess(response.msg || "执行成功");
              
              // 处理执行结果
              if (response.data) {
                if (typeof response.data === 'string') {
                  if (response.data.startsWith('http')) {
                    // 如果返回的是下载链接，则触发下载
                    this.downloadTemplateResult(response.data);
                  } else {
                    // 普通文本结果，显示预览
                    this.showExecuteResult('text', response.data);
                  }
                } else {
                  // JSON结果，显示预览
                  this.showExecuteResult('json', response.data);
                }
              }
              
              this.executeTemplate.open = false;
            } else {
              this.$modal.msgError(response.msg || "执行失败");
            }
          }).catch(error => {
            // 如果是二进制响应，可能是直接下载的情况，不需要处理错误
            if (error.response && error.response.status === 200) {
              this.$modal.msgSuccess("执行成功，文件已开始下载");
              this.executeTemplate.open = false;
            } else {
              this.$modal.msgError(error.message || "执行失败");
            }
          }).finally(() => {
            this.executeTemplate.loading = false;
          });
        }
      });
    },
    
    /** 下载模板执行结果 */
    downloadTemplateResult(url) {
      // 创建一个隐藏的链接元素
      const link = document.createElement('a');
      link.style.display = 'none';
      link.href = url;
      link.setAttribute('download', ''); // 这会使用服务器提供的文件名
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    },
    // 模板方法管理相关操作
    handleTemplateMethodManage() {
      this.templateMethodManage.open = true;
      this.templateMethodManage.activeTab = 'list';
      this.getMethodList();
    },
    getMethodList() {
      this.templateMethodManage.loading = true;
      listTemplateMethods().then(response => {
        this.templateMethodManage.methodList = response.data;
        this.templateMethodManage.loading = false;
      });
    },
    handleAddMethod() {
      this.templateMethodManage.formOpen = true;
      this.templateMethodManage.title = "添加模板方法";
      this.templateMethodManage.form = {
        methodId: undefined,
        methodName: undefined,
        methodDesc: undefined,
        handlerClass: undefined,
        orderNum: 0,
        status: "0",
        remark: undefined
      };
    },
    handleEditMethod(row) {
      this.templateMethodManage.formOpen = true;
      this.templateMethodManage.title = "修改模板方法";
      this.templateMethodManage.form = { ...row };
    },
    handleDeleteMethod(row) {
      this.$modal.confirm('是否确认删除方法"' + row.methodName + '"的数据项?').then(function() {
        return delFile(row.methodId);
      }).then(() => {
        this.getMethodList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    handleMethodStatusChange(row) {
      this.$modal.confirm('是否确认修改方法状态为"' + (row.status === '0' ? '启用' : '禁用') + '"的数据项?').then(function() {
        return updateFile({
          fileId: row.methodId,
          status: row.status === '0' ? '1' : '0'
        });
      }).then(() => {
        this.getMethodList();
        this.$modal.msgSuccess("修改成功");
      }).catch(() => {});
    },
    handleFileRequirements(row) {
      this.fileReqForm.methodId = row.methodId;
      this.fileReqForm.methodName = row.methodName;
      
      // 获取方法的文件需求
      getMethodFileRequirements(row.methodId).then(response => {
        this.fileReqList = response.data || [];
        this.fileReqOpen = true;
      });
    },
    getMethodFileReqList() {
      // 已经在handleFileRequirements中获取了方法的文件需求
    },
    handleAddFileReq() {
      this.currentFileReq = {
        methodId: this.fileReqForm.methodId,
        fileIndex: this.fileReqList.length,
        fileDesc: '',
        fileType: 'excel',
        fileSource: 'U',
        isRequired: 'Y',
        filePattern: '.*\\.xlsx?',
        remark: ''
      };
      this.fileReqEditIndex = -1;
      this.fileReqDialogTitle = "添加文件需求";
      this.fileReqFormOpen = true;
    },
    handleEditFileReq(row) {
      this.currentFileReq = JSON.parse(JSON.stringify(row));
      this.fileReqEditIndex = this.fileReqList.findIndex(item => item.reqId === row.reqId);
      this.fileReqDialogTitle = "编辑文件需求";
      this.fileReqFormOpen = true;
    },
    handleDeleteFileReq(index) {
      this.$modal.confirm('确认删除该文件需求吗？').then(() => {
        this.fileReqList.splice(index, 1);
        // 重新排序 fileIndex
        this.fileReqList.forEach((item, idx) => {
          item.fileIndex = idx;
        });
      }).catch(() => {});
    },
    submitFileReqForm() {
      this.$refs["fileReqForm"].validate(valid => {
        if (valid) {
          if (this.fileReqEditIndex !== -1) {
            // 编辑现有项
            this.fileReqList.splice(this.fileReqEditIndex, 1, this.currentFileReq);
          } else {
            // 添加新项
            this.fileReqList.push(this.currentFileReq);
          }
          
          this.fileReqFormOpen = false;
        }
      });
    },
    cancelFileReq() {
      this.fileReqFormOpen = false;
    },
    submitFileReqList() {
      const data = {
        method: { methodId: this.fileReqForm.methodId },
        fileReqs: this.fileReqList
      };
      
      saveTemplateMethod(data).then(response => {
        this.$modal.msgSuccess("保存成功");
        this.fileReqOpen = false;
      });
    },
    submitMethodForm() {
      this.$refs["methodForm"].validate(valid => {
        if (valid) {
          const data = {
            method: this.templateMethodManage.form
          };
          
          saveTemplateMethod(data).then(response => {
            this.$modal.msgSuccess("保存成功");
            this.templateMethodManage.formOpen = false;
            this.getMethodList();
          });
        }
      });
    },
    cancelMethod() {
      this.templateMethodManage.formOpen = false;
    },
    /** 显示执行结果预览 */
    showExecuteResult(type, data) {
      this.executeResult.type = type;
      this.executeResult.data = data;
      this.executeResult.open = true;
    },
    /** 格式化JSON显示 */
    formatJson(json) {
      try {
        return JSON.stringify(json, null, 2);
      } catch (e) {
        return json;
      }
    },
  }
};
</script>

<style scoped>
.preview-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}
.el-upload-dragger {
  width: 100%;
}
.method-description {
  background-color: #f8f8f8;
  padding: 10px;
  border-radius: 4px;
  border-left: 3px solid #409EFF;
  color: #606266;
  font-size: 14px;
  margin-bottom: 10px;
}
.file-req-help {
  margin-left: 8px;
  color: #909399;
  cursor: help;
}
.result-container {
  max-height: 500px;
  overflow-y: auto;
  background-color: #f5f7fa;
  padding: 10px;
  border-radius: 4px;
}
.result-container pre {
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
}
</style> 