import request from '@/utils/request'

// 查询账户交易明细列表
export function listTrade_detail(query) {
  return request({
    url: '/data/trade_detail/list',
    method: 'get',
    params: query
  })
}

// 查询账户交易明细详细
export function getTrade_detail(id) {
  return request({
    url: '/data/trade_detail/' + id,
    method: 'get'
  })
}

// 新增账户交易明细
export function addTrade_detail(data) {
  return request({
    url: '/data/trade_detail',
    method: 'post',
    data: data
  })
}

// 修改账户交易明细
export function updateTrade_detail(data) {
  return request({
    url: '/data/trade_detail',
    method: 'put',
    data: data
  })
}

// 删除账户交易明细
export function delTrade_detail(id) {
  return request({
    url: '/data/trade_detail/' + id,
    method: 'delete'
  })
}

// 导出账户交易明细
export function exportTradeDetail(query) {
  return request({
    url: '/data/trade_detail/export',
    method: 'post',
    data: query
  })
}

// 流式导出账户交易明细（支持大数据量导出）
export function streamExportTradeDetail(query) {
  return request({
    url: '/data/trade_detail/streamExport',
    method: 'post',
    data: query,
    responseType: 'blob'
  })
}

// 更新店铺数据
export function updateShopData() {
  return request({
    url: '/data/trade_detail/updateShopData',
    method: 'post'
  })
}
