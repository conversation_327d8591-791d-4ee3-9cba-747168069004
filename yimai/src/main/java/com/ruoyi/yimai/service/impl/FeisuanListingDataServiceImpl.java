package com.ruoyi.yimai.service.impl;

import java.util.List;
import java.util.ArrayList;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.yimai.mapper.FeisuanListingDataMapper;
import com.ruoyi.yimai.domain.FeisuanListingData;
import com.ruoyi.yimai.service.IFeisuanListingDataService;
import com.ruoyi.yimai.service.IAmazonAccountService;
import com.ruoyi.yimai.domain.AmazonAccount;
import com.ruoyi.common.utils.feishu.FeishuMessageUtils;
import com.ruoyi.yimai.mapper.CustomerMapper;
import com.ruoyi.yimai.mapper.ShopMapper;
import java.text.SimpleDateFormat;
import java.util.Date;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.lang.StringBuilder;
import java.util.Calendar;

/**
 * 飞算已刊登数据Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-03-25
 */
@Service
public class FeisuanListingDataServiceImpl implements IFeisuanListingDataService 
{
    private static final Logger log = LoggerFactory.getLogger(FeisuanListingDataServiceImpl.class);

    @Autowired
    private FeisuanListingDataMapper feisuanListingDataMapper;
    
    @Autowired
    private IAmazonAccountService amazonAccountService;
    
    @Autowired
    private CustomerMapper customerMapper;
    
    @Autowired
    private ShopMapper shopMapper;

    /**
     * 查询飞算已刊登数据
     * 
     * @param id 飞算已刊登数据主键
     * @return 飞算已刊登数据
     */
    @Override
    public FeisuanListingData selectFeisuanListingDataById(Long id)
    {
        return feisuanListingDataMapper.selectFeisuanListingDataById(id);
    }

    /**
     * 查询飞算已刊登数据列表
     * 
     * @param feisuanListingData 飞算已刊登数据
     * @return 飞算已刊登数据
     */
    @Override
    public List<FeisuanListingData> selectFeisuanListingDataList(FeisuanListingData feisuanListingData)
    {
        return feisuanListingDataMapper.selectFeisuanListingDataList(feisuanListingData);
    }

    /**
     * 新增飞算已刊登数据
     * 
     * @param feisuanListingData 飞算已刊登数据
     * @return 结果
     */
    @Override
    public int insertFeisuanListingData(FeisuanListingData feisuanListingData)
    {
        return feisuanListingDataMapper.insertFeisuanListingData(feisuanListingData);
    }

    /**
     * 修改飞算已刊登数据
     * 
     * @param feisuanListingData 飞算已刊登数据
     * @return 结果
     */
    @Override
    public int updateFeisuanListingData(FeisuanListingData feisuanListingData)
    {
        return feisuanListingDataMapper.updateFeisuanListingData(feisuanListingData);
    }

    /**
     * 批量删除飞算已刊登数据
     * 
     * @param ids 需要删除的飞算已刊登数据主键
     * @return 结果
     */
    @Override
    public int deleteFeisuanListingDataByIds(Long[] ids)
    {
        return feisuanListingDataMapper.deleteFeisuanListingDataByIds(ids);
    }

    /**
     * 删除飞算已刊登数据信息
     * 
     * @param id 飞算已刊登数据主键
     * @return 结果
     */
    @Override
    public int deleteFeisuanListingDataById(Long id)
    {
        return feisuanListingDataMapper.deleteFeisuanListingDataById(id);
    }

    /**
     * 获取已刊登的SKU列表
     * 
     * @param skuList 要检查的SKU列表
     * @return 已刊登的SKU列表
     */
    @Override
    public List<String> getListedSkus(List<String> skuList) {
        if (skuList == null || skuList.isEmpty()) {
            return new ArrayList<>();
        }
        
        // 查询数据库中已经存在的SKU
        List<FeisuanListingData> listedData = feisuanListingDataMapper.selectFeisuanListingDataBySkus(skuList);
        
        // 提取SKU列表
        return listedData.stream()
                .map(FeisuanListingData::getSku)
                .filter(sku -> sku != null && !sku.isEmpty())
                .collect(Collectors.toList());
    }
    
    /**
     * 根据taskId获取已刊登的SKU列表
     * 
     * @param skuList 要检查的SKU列表
     * @param taskId 任务ID
     * @return 已刊登的SKU列表及其状态
     */
    @Override
    public List<String> getListedSkusByTaskId(List<String> skuList, Long taskId) {
        if (skuList == null || skuList.isEmpty() || taskId == null) {
            return new ArrayList<>();
        }
        
        // 查询数据库中已经存在的SKU
        List<FeisuanListingData> listedData = feisuanListingDataMapper.selectFeisuanListingDataBySkusAndTaskId(skuList, taskId);
        
        // 提取SKU列表
        return listedData.stream()
                .map(FeisuanListingData::getSku)
                .filter(sku -> sku != null && !sku.isEmpty())
                .collect(Collectors.toList());
    }
    
    /**
     * 根据SKU列表和任务ID获取已刊登数据
     * 
     * @param skuList 要检查的SKU列表
     * @param taskId 任务ID
     * @return 已刊登数据列表
     */
    @Override
    public List<FeisuanListingData> getListedDataBySkusAndTaskId(List<String> skuList, Long taskId) {
        if (skuList == null || skuList.isEmpty() || taskId == null) {
            return new ArrayList<>();
        }
        
        // 查询数据库中已经存在的SKU
        return feisuanListingDataMapper.selectFeisuanListingDataBySkusAndTaskId(skuList, taskId);
    }
    
    /**
     * 获取指定店铺当天成功刊登的数据
     *
     * @param store 店铺名称
     * @return 成功刊登的数据列表
     */
    @Override
    public List<FeisuanListingData> getSuccessListingDataByStoreToday(String store) {
        if (store == null || store.isEmpty()) {
            return new ArrayList<>();
        }
        
        return feisuanListingDataMapper.selectSuccessListingDataByStoreToday(store);
    }

    /**
     * 根据任务ID和状态删除飞算已刊登数据
     *
     * @param taskId 任务ID
     * @param status 状态（可为null，表示不限制状态）
     * @return 结果
     */
    @Override
    public int deleteFeisuanListingDataByTaskIdAndStatus(Long taskId, String status) {
        return feisuanListingDataMapper.deleteFeisuanListingDataByTaskIdAndStatus(taskId, status);
    }
    
    /**
     * 发送今日店铺刊登统计数据到飞书
     */
    @Override
    public void sendTodayStatsToFeishu() {
        log.info("开始执行店铺刊登统计任务...");
        
        try {
            // 飞书webhook key
            final String FEISHU_WEBHOOK_KEY = "1507525a-90c6-4293-85c0-6079621cd3e7";
            
            // 获取昨天日期
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.DAY_OF_MONTH, -1);
            String yesterday = sdf.format(calendar.getTime());
            log.info("统计日期: {}", yesterday);
            
            // 获取所有账号对应的店铺列表
            List<AmazonAccount> accounts = amazonAccountService.selectAmazonAccountList(new AmazonAccount());
            // 从账号中提取非空店铺名
            List<String> stores = accounts.stream()
                                     .map(AmazonAccount::getStore)
                                     .filter(store -> store != null && !store.isEmpty())
                                     .distinct()
                                     .collect(Collectors.toList());
            
            if (stores.isEmpty()) {
                log.info("没有找到任何店铺数据");
                return;
            }
            
            log.info("找到店铺数量: {}, 店铺列表: {}", stores.size(), stores);
            
            // 构建消息内容
            StringBuilder messageBuilder = new StringBuilder();
            messageBuilder.append("# 昨日店铺刊登统计 (").append(yesterday).append(")\n\n");
            messageBuilder.append("| 店铺 | 成功刊登数量 | 总刊登数量 | 成功率 |\n");
            messageBuilder.append("|------|------|------|------|\n");
            
            int totalSuccess = 0;
            int totalCount = 0;
            
            for (String store : stores) {
                // 查询成功刊登数量
                int successCount = feisuanListingDataMapper.countListingDataByStoreAndStatus(
                        store, "success", yesterday, yesterday);
                
                // 查询总刊登数量
                int totalStoreCount = feisuanListingDataMapper.countListingDataByStore(
                        store, yesterday, yesterday);
                
                log.info("店铺[{}]统计结果: 成功数量={}, 总数量={}", store, successCount, totalStoreCount);
                
                // 如果昨日没有数据则跳过
                if (totalStoreCount == 0) {
                    continue;
                }
                
                // 计算成功率
                double successRate = totalStoreCount > 0 ? (double) successCount / totalStoreCount * 100 : 0;
                String successRateStr = String.format("%.2f%%", successRate);
                
                // 添加到消息内容
                messageBuilder.append("| ")
                              .append(store).append(" | ")
                              .append(successCount).append(" | ")
                              .append(totalStoreCount).append(" | ")
                              .append(successRateStr).append(" |\n");
                
                totalSuccess += successCount;
                totalCount += totalStoreCount;
            }
            
            // 如果没有任何数据，发送提示消息
            if (totalCount == 0) {
                messageBuilder = new StringBuilder();
                messageBuilder.append("昨日暂无刊登数据");
                
                log.info("昨日无刊登数据，发送简单消息");
                
                // 发送消息到飞书
                boolean result = FeishuMessageUtils.sendTextMessage(FEISHU_WEBHOOK_KEY, "昨日店铺刊登统计: 昨日暂无刊登数据");
                
                if (result) {
                    log.info("昨日店铺刊登统计消息发送成功（无数据）");
                } else {
                    log.error("昨日店铺刊登统计消息发送失败（无数据）");
                }
                
                return;
            }
            
            // 添加总计行
            double totalSuccessRate = totalCount > 0 ? (double) totalSuccess / totalCount * 100 : 0;
            String totalSuccessRateStr = String.format("%.2f%%", totalSuccessRate);
            
            messageBuilder.append("| ")
                          .append("**总计**").append(" | ")
                          .append("**").append(totalSuccess).append("**").append(" | ")
                          .append("**").append(totalCount).append("**").append(" | ")
                          .append("**").append(totalSuccessRateStr).append("**").append(" |\n");
            
            String messageContent = messageBuilder.toString();
            log.info("构建的消息内容: {}", messageContent);
            
            // 发送消息到飞书
            boolean result = FeishuMessageUtils.sendRichTextMessage(FEISHU_WEBHOOK_KEY, 
                    "昨日店铺刊登统计", messageContent);
            
            if (result) {
                log.info("昨日店铺刊登统计消息发送成功");
            } else {
                log.error("昨日店铺刊登统计消息发送失败");
            }
            
        } catch (Exception e) {
            log.error("发送店铺刊登统计任务异常", e);
        }
    }
    
    /**
     * 发送每日业务统计数据到飞书
     */
    @Override
    public void sendDailyBusinessStatsToFeishu() {
        log.info("开始执行每日业务统计任务...");
        
        try {
            // 飞书webhook key
            final String FEISHU_WEBHOOK_KEY = "61232ea9-839e-4354-a1ed-ef020645640b";
            
            // 获取统计数据
            int totalCustomers = customerMapper.countTotalCustomers();
            int customersWithShops = customerMapper.countCustomersWithShops();
            int totalShops = shopMapper.countTotalShops();
            int storesWithListedProducts = feisuanListingDataMapper.countStoresWithListedProducts();
            int currentMonthListings = feisuanListingDataMapper.countCurrentMonthListings();
            
            // 获取当前日期
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            String today = sdf.format(new Date());
            
            log.info("统计数据 - 签约客户总数: {}, 有店铺的客户总数: {}, 店铺总数: {}, 有刊登商品的店铺总数: {}, 本月刊登量: {}", 
                    totalCustomers, customersWithShops, totalShops, storesWithListedProducts, currentMonthListings);
            
            // 构建消息内容
            StringBuilder messageBuilder = new StringBuilder();
            messageBuilder.append("# 每日业务统计 (").append(today).append(")\n\n");
            messageBuilder.append("| 指标 | 数量 |\n");
            messageBuilder.append("|------|------|\n");
            messageBuilder.append("| 签约客户总数 | ").append(totalCustomers).append(" |\n");
            messageBuilder.append("| 有店铺的客户总数 | ").append(customersWithShops).append(" |\n");
            messageBuilder.append("| 店铺总数 | ").append(totalShops).append(" |\n");
            messageBuilder.append("| 有刊登商品的店铺总数 | ").append(storesWithListedProducts).append(" |\n");
            messageBuilder.append("| 本月刊登量 | ").append(currentMonthListings).append(" |\n");
            
            String messageContent = messageBuilder.toString();
            log.info("构建的消息内容: {}", messageContent);
            
            // 发送消息到飞书
            boolean result = FeishuMessageUtils.sendRichTextMessage(FEISHU_WEBHOOK_KEY, 
                    "每日业务统计", messageContent);
            
            if (result) {
                log.info("每日业务统计消息发送成功");
            } else {
                log.error("每日业务统计消息发送失败");
            }
            
        } catch (Exception e) {
            log.error("发送每日业务统计任务异常", e);
        }
    }
    
    /**
     * 根据店铺名称和时间区间统计刊登数量
     *
     * @param store 店铺名称
     * @param startDate 开始日期 (yyyy-MM-dd)
     * @param endDate 结束日期 (yyyy-MM-dd)
     * @return 刊登数量（只统计success状态的数据）
     */
    @Override
    public int countListingDataByStore(String store, String startDate, String endDate) {
        return feisuanListingDataMapper.countListingDataByStoreAndStatus(store, "success", startDate, endDate);
    }
}
