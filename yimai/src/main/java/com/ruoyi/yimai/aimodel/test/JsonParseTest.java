package com.ruoyi.yimai.aimodel.test;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.yimai.aimodel.pojo.ProductInfo;
import com.ruoyi.yimai.aimodel.utils.ApiClient;
import com.ruoyi.yimai.aimodel.utils.DSR1ModelUtil;

import java.util.List;

/**
 * JSON解析测试类
 * 用于测试从API响应中提取和解析JSON内容
 */
public class JsonParseTest {

    /**
     * 测试从响应中提取JSON内容
     */
    public static void testExtractJson() {
        System.out.println("===== 测试提取JSON内容 =====");
        
        // 示例1：标准JSON响应
        String response1 = "模型思考后得出：{\"product_title\":\"高品质耳机\",\"search_keywords\":\"耳机,音质好,降噪\",\"product_selling_points\":[\"点1\",\"点2\",\"点3\",\"点4\",\"点5\"]}";
        String json1 = ApiClient.extractContentBetweenBrackets(response1);
        System.out.println("示例1原始响应: " + response1);
        System.out.println("提取结果: " + json1);
        System.out.println();
        
        // 示例2：多行JSON响应
        String response2 = "我分析后认为最好的产品信息如下：\n{\n  \"product_title\": \"超长续航蓝牙耳机\",\n  \"search_keywords\": [\"蓝牙耳机\", \"长续航\", \"降噪\"],\n  \"product_selling_points\": [\n    \"超长40小时续航\",\n    \"主动降噪\",\n    \"舒适贴合\",\n    \"高清音质\",\n    \"快速充电\"\n  ]\n}";
        String json2 = ApiClient.extractContentBetweenBrackets(response2);
        System.out.println("示例2原始响应: " + response2);
        System.out.println("提取结果: " + json2);
        System.out.println();
        
        // 示例3：含有解释的JSON响应
        String response3 = "根据您的需求，我为这款产品设计了以下信息：\n\n```json\n{\n  \"product_title\": \"智能手表健康监测\",\n  \"search_keywords\": \"智能手表,健康监测,心率监测\",\n  \"product_selling_points\": [\"全天候心率监测\", \"睡眠质量分析\", \"运动模式跟踪\", \"防水设计\", \"长达7天续航\"]\n}\n```\n\n希望这些信息符合您的要求！";
        String json3 = ApiClient.extractContentBetweenBrackets(response3);
        System.out.println("示例3原始响应: " + response3);
        System.out.println("提取结果: " + json3);
        System.out.println();
    }
    
    /**
     * 测试解析JSON到ProductInfo
     */
    public static void testParseProductInfo() {
        System.out.println("===== 测试解析ProductInfo =====");
        
        // 示例1：标准JSON
        String json1 = "{\"product_title\":\"高品质降噪耳机\",\"search_keywords\":\"耳机,降噪,音质好\",\"product_selling_points\":[\"主动降噪技术\",\"高清音质体验\",\"舒适贴合设计\",\"30小时超长续航\",\"快速充电功能\"]}";
        ProductInfo info1 = DSR1ModelUtil.parseProductInfo(json1);
        printProductInfo(info1);
        
        // 示例2：关键词是数组
        String json2 = "{\"product_title\":\"超薄笔记本电脑\",\"search_keywords\":[\"笔记本电脑\",\"超薄\",\"轻便\",\"高性能\"],\"product_selling_points\":[\"仅重1.2kg轻薄设计\",\"全高清IPS显示屏\",\"强劲性能处理器\",\"快速SSD存储\",\"全天候电池续航\"]}";
        ProductInfo info2 = DSR1ModelUtil.parseProductInfo(json2);
        printProductInfo(info2);
        
        // 示例3：格式错误的JSON
        String json3 = "{\"product_title\":\"智能手机\",\"search_keywords\":\"手机,智能,高清相机\",\"product_selling_points\":[\"点1\",\"点2\"]}";
        ProductInfo info3 = DSR1ModelUtil.parseProductInfo(json3);
        printProductInfo(info3);
    }
    
    /**
     * 打印ProductInfo对象
     */
    private static void printProductInfo(ProductInfo info) {
        System.out.println("商品标题: " + info.getProductTitle());
        System.out.println("搜索关键词: " + info.getSearchKeywords());
        System.out.println("商品卖点:");
        List<String> points = info.getProductSellingPoints();
        if (points != null) {
            for (int i = 0; i < points.size(); i++) {
                System.out.println("  " + (i + 1) + ". " + points.get(i));
            }
        }
        System.out.println();
    }
    
    /**
     * 主方法
     */
    public static void main(String[] args) {
        System.out.println("开始JSON解析测试...");
        
        // 测试从响应中提取JSON
        testExtractJson();
        
        // 测试解析ProductInfo
        testParseProductInfo();
        
        System.out.println("测试完成");
    }
} 