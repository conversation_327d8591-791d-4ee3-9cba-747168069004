package com.ruoyi.yimai.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.yimai.domain.Shop;
import com.ruoyi.yimai.service.IShopService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 店铺Controller
 * 
 * <AUTHOR>
 * @date 2025-04-28
 */
@RestController
@RequestMapping("/yimai/shop")
public class ShopController extends BaseController
{
    @Autowired
    private IShopService shopService;

    /**
     * 查询店铺列表
     */
    @PreAuthorize("@ss.hasPermi('yimai:shop:list')")
    @GetMapping("/list")
    public TableDataInfo list(Shop shop)
    {
        startPage();
        List<Shop> list = shopService.selectShopList(shop);
        return getDataTable(list);
    }

    /**
     * 根据客户ID查询店铺列表
     */
    @PreAuthorize("@ss.hasPermi('yimai:shop:list')")
    @GetMapping("/listByCustomerId/{customerId}")
    public AjaxResult listByCustomerId(@PathVariable("customerId") Long customerId)
    {
        List<Shop> list = shopService.selectShopListByCustomerId(customerId);
        return success(list);
    }

    /**
     * 导出店铺列表
     */
    @PreAuthorize("@ss.hasPermi('yimai:shop:export')")
    @Log(title = "店铺", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Shop shop)
    {
        List<Shop> list = shopService.selectShopList(shop);
        ExcelUtil<Shop> util = new ExcelUtil<Shop>(Shop.class);
        util.exportExcel(response, list, "店铺数据");
    }

    /**
     * 获取店铺详细信息
     */
    @PreAuthorize("@ss.hasPermi('yimai:shop:query')")
    @GetMapping(value = "/{shopId}")
    public AjaxResult getInfo(@PathVariable("shopId") Long shopId)
    {
        return success(shopService.selectShopByShopId(shopId));
    }

    /**
     * 新增店铺
     */
    @PreAuthorize("@ss.hasPermi('yimai:shop:add')")
    @Log(title = "店铺", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Shop shop)
    {
        return toAjax(shopService.insertShop(shop));
    }

    /**
     * 修改店铺
     */
    @PreAuthorize("@ss.hasPermi('yimai:shop:edit')")
    @Log(title = "店铺", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Shop shop)
    {
        return toAjax(shopService.updateShop(shop));
    }

    /**
     * 删除店铺
     */
    @PreAuthorize("@ss.hasPermi('yimai:shop:remove')")
    @Log(title = "店铺", businessType = BusinessType.DELETE)
	@DeleteMapping("/{shopIds}")
    public AjaxResult remove(@PathVariable Long[] shopIds)
    {
        return toAjax(shopService.deleteShopByShopIds(shopIds));
    }

    /**
     * 导出空的店铺数据作为模板
     */
    @PreAuthorize("@ss.hasPermi('yimai:shop:export')")
    @GetMapping("/exportEmpty")
    public void exportEmpty(HttpServletResponse response)
    {
        List<Shop> list = java.util.Collections.emptyList();
        ExcelUtil<Shop> util = new ExcelUtil<Shop>(Shop.class);
        util.exportExcel(response, list, "店铺导入模板");
    }
    
    /**
     * 导入店铺数据（文件上传方式）
     */
    @PreAuthorize("@ss.hasPermi('yimai:shop:import')")
    @Log(title = "店铺", businessType = BusinessType.IMPORT)
    @PostMapping("/importFile")
    public AjaxResult importShopsByFile(@RequestParam("file") MultipartFile file, 
                                       @RequestParam("strategy") String strategy)
    {
        try {
            // 直接执行导入（跳过或覆盖策略）
            boolean result = shopService.importShopsFromFileAndConfirm(file, strategy);
            return toAjax(result);
        } catch (Exception e) {
            return error("导入失败：" + e.getMessage());
        }
    }

    /**
     * 飞书专用：导入店铺数据（文件上传方式，无需登录和权限）
     */
    @com.ruoyi.common.annotation.Anonymous
    @PostMapping("/importFileByFeishu")
    public AjaxResult importShopsByFeishu(@RequestParam("sheet") MultipartFile file) {
        try {
            // 策略写死为cover
            String strategy = "cover";
            boolean result = shopService.importShopsFromFileAndConfirm(file, strategy);
            return toAjax(result);
        } catch (Exception e) {
            return error("导入失败：" + e.getMessage());
        }
    }
}
