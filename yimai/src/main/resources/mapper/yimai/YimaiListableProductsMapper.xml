<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.yimai.mapper.YimaiListableProductsMapper">
    
    <resultMap type="YimaiListableProducts" id="YimaiListableProductsResult">
        <result property="systemSku"    column="system_sku"    />
        <result property="englishName"    column="english_name"    />
        <result property="chineseName"    column="chinese_name"    />
        <result property="productCategory"    column="product_category"    />
        <result property="lengthCm"    column="length_cm"    />
        <result property="widthCm"    column="width_cm"    />
        <result property="heightCm"    column="height_cm"    />
        <result property="netWeightG"    column="net_weight_g"    />
        <result property="grossWeightG"    column="gross_weight_g"    />
        <result property="avgOutboundWeightG"    column="avg_outbound_weight_g"    />
        <result property="price"    column="price"    />
        <result property="overseasStockQty"    column="overseas_stock_qty"    />
        <result property="domesticStockQty"    column="domestic_stock_qty"    />
        <result property="shippingWarehouse"    column="shipping_warehouse"    />
        <result property="destinationCountry"    column="destination_country"    />
        <result property="priceShippingIncluded"    column="price_shipping_included"    />
        <result property="minOrderQty"    column="min_order_qty"    />
        <result property="productStatus"    column="product_status"    />
        <result property="supplyStatus"    column="supply_status"    />
        <result property="listingStatus"    column="listing_status"    />
        <result property="isInfringementProhibited"    column="is_infringement_prohibited"    />
        <result property="infringementCountryType"    column="infringement_country_type"    />
        <result property="prohibitedPlatforms"    column="prohibited_platforms"    />
        <result property="updateTime"    column="update_time"    />
        <result property="productAttributes"    column="product_attributes"    />
        <result property="attributeCategory"    column="attribute_category"    />
        <result property="procurementLeadTime"    column="procurement_lead_time"    />
        <result property="procurementWeeks"    column="procurement_weeks"    />
        <result property="latestInboundDimensions"    column="latest_inbound_dimensions"    />
        <result property="consignmentSupplier"    column="consignment_supplier"    />
        <result property="productCost"    column="product_cost"    />
        <result property="overseasShippingFee"    column="overseas_shipping_fee"    />
        <result property="overseasWarehouseFee"    column="overseas_warehouse_fee"    />
        <result property="isAvailable"    column="is_available"    />
        <result property="score"    column="score"    />
    </resultMap>

    <sql id="selectYimaiListableProductsVo">
        select system_sku, english_name, chinese_name, product_category, length_cm, width_cm, height_cm, net_weight_g, gross_weight_g, avg_outbound_weight_g, price, overseas_stock_qty, domestic_stock_qty, shipping_warehouse, destination_country, price_shipping_included, min_order_qty, product_status, supply_status, listing_status, is_infringement_prohibited, infringement_country_type, prohibited_platforms, update_time, product_attributes, attribute_category, procurement_lead_time, procurement_weeks, latest_inbound_dimensions, consignment_supplier, product_cost, overseas_shipping_fee, overseas_warehouse_fee, is_available, score from yimai_listable_products
    </sql>

    <select id="selectYimaiListableProductsList" parameterType="YimaiListableProducts" resultMap="YimaiListableProductsResult">
        <include refid="selectYimaiListableProductsVo"/>
        <where>  
            <if test="systemSku != null  and systemSku != ''"> and system_sku = #{systemSku}</if>
            <if test="englishName != null  and englishName != ''"> and english_name like concat('%', #{englishName}, '%')</if>
            <if test="chineseName != null  and chineseName != ''"> and chinese_name like concat('%', #{chineseName}, '%')</if>
            <if test="productCategory != null  and productCategory != ''"> and product_category = #{productCategory}</if>
            <if test="lengthCm != null "> and length_cm = #{lengthCm}</if>
            <if test="widthCm != null "> and width_cm = #{widthCm}</if>
            <if test="heightCm != null "> and height_cm = #{heightCm}</if>
            <if test="netWeightG != null "> and net_weight_g = #{netWeightG}</if>
            <if test="grossWeightG != null "> and gross_weight_g = #{grossWeightG}</if>
            <if test="avgOutboundWeightG != null "> and avg_outbound_weight_g = #{avgOutboundWeightG}</if>
            <if test="price != null "> and price = #{price}</if>
            <if test="overseasStockQty != null "> and overseas_stock_qty = #{overseasStockQty}</if>
            <if test="domesticStockQty != null "> and domestic_stock_qty = #{domesticStockQty}</if>
            <if test="shippingWarehouse != null  and shippingWarehouse != ''"> and shipping_warehouse = #{shippingWarehouse}</if>
            <if test="destinationCountry != null  and destinationCountry != ''"> and destination_country = #{destinationCountry}</if>
            <if test="priceShippingIncluded != null "> and price_shipping_included = #{priceShippingIncluded}</if>
            <if test="minOrderQty != null "> and min_order_qty = #{minOrderQty}</if>
            <if test="productStatus != null  and productStatus != ''"> and product_status = #{productStatus}</if>
            <if test="supplyStatus != null  and supplyStatus != ''"> and supply_status = #{supplyStatus}</if>
            <if test="listingStatus != null  and listingStatus != ''"> and listing_status = #{listingStatus}</if>
            <if test="isInfringementProhibited != null "> and is_infringement_prohibited = #{isInfringementProhibited}</if>
            <if test="infringementCountryType != null  and infringementCountryType != ''"> and infringement_country_type = #{infringementCountryType}</if>
            <if test="prohibitedPlatforms != null  and prohibitedPlatforms != ''"> and prohibited_platforms = #{prohibitedPlatforms}</if>
            <if test="productAttributes != null  and productAttributes != ''"> and product_attributes = #{productAttributes}</if>
            <if test="attributeCategory != null  and attributeCategory != ''"> and attribute_category = #{attributeCategory}</if>
            <if test="procurementLeadTime != null  and procurementLeadTime != ''"> and procurement_lead_time = #{procurementLeadTime}</if>
            <if test="procurementWeeks != null "> and procurement_weeks = #{procurementWeeks}</if>
            <if test="latestInboundDimensions != null  and latestInboundDimensions != ''"> and latest_inbound_dimensions = #{latestInboundDimensions}</if>
            <if test="consignmentSupplier != null  and consignmentSupplier != ''"> and consignment_supplier = #{consignmentSupplier}</if>
            <if test="productCost != null "> and product_cost = #{productCost}</if>
            <if test="overseasShippingFee != null "> and overseas_shipping_fee = #{overseasShippingFee}</if>
            <if test="overseasWarehouseFee != null "> and overseas_warehouse_fee = #{overseasWarehouseFee}</if>
            <if test="isAvailable != null "> and is_available = #{isAvailable}</if>
        </where>
    </select>
    
    <select id="selectYimaiListableProductsBySystemSku" parameterType="String" resultMap="YimaiListableProductsResult">
        <include refid="selectYimaiListableProductsVo"/>
        where system_sku = #{systemSku}
    </select>

    <insert id="insertYimaiListableProducts" parameterType="YimaiListableProducts" useGeneratedKeys="true" keyProperty="systemSku">
        insert into yimai_listable_products
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="systemSku != null">system_sku,</if>
            <if test="englishName != null">english_name,</if>
            <if test="chineseName != null">chinese_name,</if>
            <if test="productCategory != null">product_category,</if>
            <if test="lengthCm != null">length_cm,</if>
            <if test="widthCm != null">width_cm,</if>
            <if test="heightCm != null">height_cm,</if>
            <if test="netWeightG != null">net_weight_g,</if>
            <if test="grossWeightG != null">gross_weight_g,</if>
            <if test="avgOutboundWeightG != null">avg_outbound_weight_g,</if>
            <if test="price != null">price,</if>
            <if test="overseasStockQty != null">overseas_stock_qty,</if>
            <if test="domesticStockQty != null">domestic_stock_qty,</if>
            <if test="shippingWarehouse != null">shipping_warehouse,</if>
            <if test="destinationCountry != null">destination_country,</if>
            <if test="priceShippingIncluded != null">price_shipping_included,</if>
            <if test="minOrderQty != null">min_order_qty,</if>
            <if test="productStatus != null">product_status,</if>
            <if test="supplyStatus != null">supply_status,</if>
            <if test="listingStatus != null">listing_status,</if>
            <if test="isInfringementProhibited != null">is_infringement_prohibited,</if>
            <if test="infringementCountryType != null">infringement_country_type,</if>
            <if test="prohibitedPlatforms != null">prohibited_platforms,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="productAttributes != null">product_attributes,</if>
            <if test="attributeCategory != null">attribute_category,</if>
            <if test="procurementLeadTime != null">procurement_lead_time,</if>
            <if test="procurementWeeks != null">procurement_weeks,</if>
            <if test="latestInboundDimensions != null">latest_inbound_dimensions,</if>
            <if test="consignmentSupplier != null">consignment_supplier,</if>
            <if test="productCost != null">product_cost,</if>
            <if test="overseasShippingFee != null">overseas_shipping_fee,</if>
            <if test="overseasWarehouseFee != null">overseas_warehouse_fee,</if>
            <if test="isAvailable != null">is_available,</if>
            <if test="score != null">score,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="systemSku != null">#{systemSku},</if>
            <if test="englishName != null">#{englishName},</if>
            <if test="chineseName != null">#{chineseName},</if>
            <if test="productCategory != null">#{productCategory},</if>
            <if test="lengthCm != null">#{lengthCm},</if>
            <if test="widthCm != null">#{widthCm},</if>
            <if test="heightCm != null">#{heightCm},</if>
            <if test="netWeightG != null">#{netWeightG},</if>
            <if test="grossWeightG != null">#{grossWeightG},</if>
            <if test="avgOutboundWeightG != null">#{avgOutboundWeightG},</if>
            <if test="price != null">#{price},</if>
            <if test="overseasStockQty != null">#{overseasStockQty},</if>
            <if test="domesticStockQty != null">#{domesticStockQty},</if>
            <if test="shippingWarehouse != null">#{shippingWarehouse},</if>
            <if test="destinationCountry != null">#{destinationCountry},</if>
            <if test="priceShippingIncluded != null">#{priceShippingIncluded},</if>
            <if test="minOrderQty != null">#{minOrderQty},</if>
            <if test="productStatus != null">#{productStatus},</if>
            <if test="supplyStatus != null">#{supplyStatus},</if>
            <if test="listingStatus != null">#{listingStatus},</if>
            <if test="isInfringementProhibited != null">#{isInfringementProhibited},</if>
            <if test="infringementCountryType != null">#{infringementCountryType},</if>
            <if test="prohibitedPlatforms != null">#{prohibitedPlatforms},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="productAttributes != null">#{productAttributes},</if>
            <if test="attributeCategory != null">#{attributeCategory},</if>
            <if test="procurementLeadTime != null">#{procurementLeadTime},</if>
            <if test="procurementWeeks != null">#{procurementWeeks},</if>
            <if test="latestInboundDimensions != null">#{latestInboundDimensions},</if>
            <if test="consignmentSupplier != null">#{consignmentSupplier},</if>
            <if test="productCost != null">#{productCost},</if>
            <if test="overseasShippingFee != null">#{overseasShippingFee},</if>
            <if test="overseasWarehouseFee != null">#{overseasWarehouseFee},</if>
            <if test="isAvailable != null">#{isAvailable},</if>
            <if test="score != null">#{score},</if>
         </trim>
    </insert>

    <update id="updateYimaiListableProducts" parameterType="YimaiListableProducts">
        update yimai_listable_products
        <trim prefix="SET" suffixOverrides=",">
            <if test="englishName != null">english_name = #{englishName},</if>
            <if test="chineseName != null">chinese_name = #{chineseName},</if>
            <if test="productCategory != null">product_category = #{productCategory},</if>
            <if test="lengthCm != null">length_cm = #{lengthCm},</if>
            <if test="widthCm != null">width_cm = #{widthCm},</if>
            <if test="heightCm != null">height_cm = #{heightCm},</if>
            <if test="netWeightG != null">net_weight_g = #{netWeightG},</if>
            <if test="grossWeightG != null">gross_weight_g = #{grossWeightG},</if>
            <if test="avgOutboundWeightG != null">avg_outbound_weight_g = #{avgOutboundWeightG},</if>
            <if test="price != null">price = #{price},</if>
            <if test="overseasStockQty != null">overseas_stock_qty = #{overseasStockQty},</if>
            <if test="domesticStockQty != null">domestic_stock_qty = #{domesticStockQty},</if>
            <if test="shippingWarehouse != null">shipping_warehouse = #{shippingWarehouse},</if>
            <if test="destinationCountry != null">destination_country = #{destinationCountry},</if>
            <if test="priceShippingIncluded != null">price_shipping_included = #{priceShippingIncluded},</if>
            <if test="minOrderQty != null">min_order_qty = #{minOrderQty},</if>
            <if test="productStatus != null">product_status = #{productStatus},</if>
            <if test="supplyStatus != null">supply_status = #{supplyStatus},</if>
            <if test="listingStatus != null">listing_status = #{listingStatus},</if>
            <if test="isInfringementProhibited != null">is_infringement_prohibited = #{isInfringementProhibited},</if>
            <if test="infringementCountryType != null">infringement_country_type = #{infringementCountryType},</if>
            <if test="prohibitedPlatforms != null">prohibited_platforms = #{prohibitedPlatforms},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="productAttributes != null">product_attributes = #{productAttributes},</if>
            <if test="attributeCategory != null">attribute_category = #{attributeCategory},</if>
            <if test="procurementLeadTime != null">procurement_lead_time = #{procurementLeadTime},</if>
            <if test="procurementWeeks != null">procurement_weeks = #{procurementWeeks},</if>
            <if test="latestInboundDimensions != null">latest_inbound_dimensions = #{latestInboundDimensions},</if>
            <if test="consignmentSupplier != null">consignment_supplier = #{consignmentSupplier},</if>
            <if test="productCost != null">product_cost = #{productCost},</if>
            <if test="overseasShippingFee != null">overseas_shipping_fee = #{overseasShippingFee},</if>
            <if test="overseasWarehouseFee != null">overseas_warehouse_fee = #{overseasWarehouseFee},</if>
            <if test="isAvailable != null">is_available = #{isAvailable},</if>
            <if test="score != null">score = #{score},</if>
        </trim>
        where system_sku = #{systemSku}
    </update>

    <delete id="deleteYimaiListableProductsBySystemSku" parameterType="String">
        delete from yimai_listable_products where system_sku = #{systemSku}
    </delete>

    <delete id="deleteYimaiListableProductsBySystemSkus" parameterType="String">
        delete from yimai_listable_products where system_sku in 
        <foreach item="systemSku" collection="array" open="(" separator="," close=")">
            #{systemSku}
        </foreach>
    </delete>
</mapper>